from datetime import datetime, timezone
from typing import List, Optional

from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Table
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship

from app.config import settings

# Create SQLAlchemy engine and session
engine = create_engine(settings.database_url)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

# URL-Category association table
url_category = Table(
    'url_category',
    Base.metadata,
    Column('url_id', Integer, ForeignKey('urls.id'), primary_key=True),
    Column('category_id', Integer, ForeignKey('categories.id'), primary_key=True)
)


class Category(Base):
    """Model for URL categories."""

    __tablename__ = "categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationship with URLs
    urls = relationship("URL", secondary=url_category, back_populates="categories")


class URL(Base):
    """Model for storing target URLs."""

    __tablename__ = "urls"

    id = Column(Integer, primary_key=True, index=True)
    url = Column(String(2048), unique=True, index=True, nullable=False)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    last_processed_at = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)
    include_tags = Column(String(1024), nullable=True)  # Tags to include, comma-separated
    exclude_tags = Column(String(1024), nullable=True)  # Tags to exclude, comma-separated
    process_links = Column(Boolean, default=False)  # Whether to process links in feed content

    # Relationship with results
    results = relationship("Result", back_populates="url", cascade="all, delete-orphan")

    # Relationship with categories
    categories = relationship("Category", secondary=url_category, back_populates="urls")


class Result(Base):
    """Model for storing processing results."""

    __tablename__ = "results"

    id = Column(Integer, primary_key=True, index=True)
    url_id = Column(Integer, ForeignKey("urls.id"), nullable=False)
    title = Column(String(512), nullable=True)
    source_url = Column(String(2048), nullable=True)
    markdown_content = Column(Text(length=16777215), nullable=True)  # MEDIUMTEXT for large content
    llm_analysis = Column(Text(length=16777215), nullable=True)  # MEDIUMTEXT for large analysis
    llm_type = Column(String(50), nullable=False)  # "ollama" or "gemini"
    llm_model = Column(String(100), nullable=False)
    processed_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    has_embedding = Column(Boolean, default=False)

    # Relationship with URL
    url = relationship("URL", back_populates="results")


class AggregatedResult(Base):
    """Model for storing aggregated results from multiple URLs."""

    __tablename__ = "aggregated_results"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(512), nullable=False)
    source_count = Column(Integer, nullable=False)  # Number of sources aggregated
    aggregated_analysis = Column(Text(length=16777215), nullable=False)  # The aggregated analysis (MEDIUMTEXT)
    llm_type = Column(String(50), nullable=False)  # "ollama" or "gemini"
    llm_model = Column(String(100), nullable=False)
    processed_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Store the IDs of the results that were aggregated (as a JSON string)
    result_ids = Column(String(1024), nullable=False)


class ScheduledJob(Base):
    """Model for storing scheduled jobs."""

    __tablename__ = "scheduled_jobs"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    job_id = Column(String(100), unique=True, nullable=False)  # Unique ID for the APScheduler job
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=False)
    hour = Column(Integer, nullable=False)
    minute = Column(Integer, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    last_run = Column(DateTime, nullable=True)

    # Relationship with category
    category = relationship("Category")


# Create all tables
def init_db():
    """Initialize the database by creating all tables."""
    Base.metadata.create_all(bind=engine)


# Database dependency
def get_db():
    """Get a database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
