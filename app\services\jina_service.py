import logging
from typing import Dict, Any, List, Optional
import re
import xml.etree.ElementTree as ET
from urllib.parse import urlparse, urljoin

import aiohttp
from bs4 import BeautifulSoup

# Set up logger
logger = logging.getLogger(__name__)


class JinaService:
    """Service for fetching content using Jina AI."""

    def __init__(self):
        """Initialize the Jina service."""
        self.base_url = "https://r.jina.ai/"

    def _extract_links_from_xml(self, xml_content: str) -> List[str]:
        """Extract links from XML/RSS feed content.

        Args:
            xml_content: XML/RSS content as string

        Returns:
            List of URLs found in <link> tags
        """
        try:
            links = []

            # First try using regex to extract links from item tags
            # This is more reliable for complex RSS feeds with CDATA sections
            item_pattern = r'<item[^>]*>.*?<link>\s*(?:<!\[CDATA\[\s*)?(.+?)(?:\s*\]\]>)?\s*</link>.*?</item>'
            item_links = re.findall(item_pattern, xml_content, re.DOTALL)
            if item_links:
                links.extend([link.strip() for link in item_links])
                logger.info(f"Extracted {len(links)} links from RSS items using regex")
                return links

            # If regex didn't work, try XML parsing
            try:
                # Try to parse as XML
                root = ET.fromstring(xml_content)

                # Look for link tags inside item tags (RSS format)
                for item in root.findall('.//item'):
                    link_elem = item.find('link')
                    if link_elem is not None:
                        if link_elem.text and link_elem.text.strip():
                            links.append(link_elem.text.strip())

                # If still no links, look for any link tags
                if not links:
                    for link_elem in root.findall('.//link'):
                        # For RSS feeds, link tags usually contain text content
                        if link_elem.text and link_elem.text.strip():
                            links.append(link_elem.text.strip())
                        # For Atom feeds, link tags have an href attribute
                        elif 'href' in link_elem.attrib:
                            links.append(link_elem.attrib['href'])

                logger.info(f"Extracted {len(links)} links from XML/RSS feed using XML parsing")
                return links
            except ET.ParseError:
                logger.warning("XML parsing failed, trying alternative methods")

            # If XML parsing failed, try more aggressive regex patterns
            link_pattern = r'<link[^>]*>\s*(?:<!\[CDATA\[\s*)?(.+?)(?:\s*\]\]>)?\s*</link>'
            cdata_links = re.findall(link_pattern, xml_content)
            if cdata_links:
                links = [link.strip() for link in cdata_links]
                logger.info(f"Extracted {len(links)} links using fallback regex")

            return links
        except Exception as e:
            logger.error(f"Error extracting links from XML: {str(e)}")
            return []

    async def fetch_content(self, url: str, include_tags: Optional[str] = None, exclude_tags: Optional[str] = None, process_links: bool = False) -> Dict[str, Any]:
        """Fetch content from a URL using Jina AI.

        Args:
            url: URL to fetch content from
            include_tags: Comma-separated list of HTML tags to include (only these tags will be kept)
            exclude_tags: Comma-separated list of HTML tags to exclude (these tags will be removed)
            process_links: Whether to process links found in feed content

        Returns:
            Dictionary containing title, source URL, and markdown content
        """
        # If process_links is enabled, first check if this is an RSS feed
        if process_links:
            # Try to detect if this is an RSS feed by checking the URL or making a direct request
            is_rss, feed_data = await self._check_if_rss_feed(url)

            if is_rss:
                logger.info(f"URL {url} detected as RSS feed, processing directly")
                return await self._process_rss_feed(url, feed_data, include_tags, exclude_tags)

        # If not an RSS feed or process_links is disabled, proceed with normal Jina processing
        result = await self._fetch_raw_content(url)

        # If process_links is enabled and we didn't already detect it as RSS, check the content
        if process_links and result.get("markdown_content"):
            content = result.get("markdown_content")

            # Check if content looks like XML
            content_start = content.strip().lower()
            if (content_start.startswith("<?xml") or
                content_start.startswith("<rss") or
                content_start.startswith("<feed") or
                "<rss" in content_start[:1000] or
                "<channel>" in content_start[:1000] or
                "<item" in content_start[:1000] or
                "this xml file does not appear to have any style information" in content_start[:1000]):
                logger.info(f"Content from {url} appears to be XML/RSS feed, extracting links")

                # Extract links from the feed
                links = self._extract_links_from_xml(content)

                if links:
                    logger.info(f"Found {len(links)} links in the feed: {links[:5]}{'...' if len(links) > 5 else ''}")
                else:
                    logger.warning(f"No links found in the feed from {url}")

                if links:
                    # Process each link and combine the results
                    combined_content = f"# Feed: {result.get('title', 'RSS Feed')}\n\n"
                    combined_content += f"Source: {url}\n\n"
                    combined_content += f"Found {len(links)} articles in the feed.\n\n"

                    # Process up to 10 links to avoid overloading
                    max_links = min(10, len(links))
                    processed_links = 0

                    for i, link in enumerate(links[:max_links]):
                        try:
                            logger.info(f"Processing feed link {i+1}/{max_links}: {link}")
                            # Make sure the link is a valid URL
                            if not link.startswith('http://') and not link.startswith('https://'):
                                if link.startswith('/'):
                                    # Relative URL, try to construct full URL
                                    base_url_parts = url.split('/')
                                    if len(base_url_parts) >= 3:  # Has scheme and domain
                                        domain = base_url_parts[0] + '//' + base_url_parts[2]
                                        link = domain + link
                                    else:
                                        logger.warning(f"Cannot process relative URL {link} from {url}")
                                        continue
                                else:
                                    # Try to prepend https://
                                    link = 'https://' + link

                            logger.info(f"Fetching content from link: {link}")
                            link_result = await self._fetch_raw_content(link)

                            if link_result.get("error"):
                                logger.warning(f"Error fetching content from link {link}: {link_result.get('error')}")
                                continue

                            # Get the content and clean it
                            link_content = link_result.get("markdown_content", "")
                            if link_content:
                                # First clean the content to remove image links
                                link_content = self._clean_markdown_content(link_content)

                                # Then apply tag filtering if specified
                                if (include_tags or exclude_tags):
                                    link_content = self._filter_content(link_content, include_tags, exclude_tags)

                            # Add article to combined content
                            combined_content += f"## {link_result.get('title', f'Article {i+1}')}\n\n"
                            combined_content += f"Source: {link_result.get('source_url', link)}\n\n"
                            combined_content += link_content + "\n\n---\n\n"
                            processed_links += 1

                        except Exception as e:
                            logger.error(f"Error processing link {link}: {str(e)}")

                    # Update the result with the combined content
                    result["markdown_content"] = combined_content
                    result["title"] = f"Feed: {result.get('title', 'RSS Feed')} ({processed_links} articles)"
                    result["processed_links"] = processed_links

                    logger.info(f"Processed {processed_links} links from feed {url}")
                    return result

        # If not a feed or no links to process, check for trending stories and process if needed
        if process_links and result.get("markdown_content"):
            # Check for trending stories section and extract links
            trending_links = self._extract_trending_links(result.get("markdown_content", ""))

            if trending_links:
                logger.info(f"Found {len(trending_links)} trending story links in {url}")

                # Process each link and combine the results
                combined_content = f"# Trending Stories from {result.get('title', 'Source')}\n\n"
                combined_content += f"Source: {url}\n\n"
                combined_content += f"Found {len(trending_links)} trending stories.\n\n"

                # Process up to 10 links to avoid overloading
                max_links = min(10, len(trending_links))
                processed_links = 0

                for i, link in enumerate(trending_links[:max_links]):
                    try:
                        logger.info(f"Processing trending story {i+1}/{max_links}: {link}")

                        # Make sure the link is a valid URL
                        if not link.startswith('http://') and not link.startswith('https://'):
                            if link.startswith('/'):
                                # Relative URL, try to construct full URL
                                parsed_url = urlparse(url)
                                base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
                                link = urljoin(base_url, link)
                            else:
                                # Try to prepend https://
                                link = 'https://' + link

                        logger.info(f"Fetching content from trending story: {link}")
                        link_result = await self._fetch_raw_content(link)

                        if link_result.get("error"):
                            logger.warning(f"Error fetching content from link {link}: {link_result.get('error')}")
                            continue

                        # Get the content and clean it
                        link_content = link_result.get("markdown_content", "")
                        if link_content:
                            # First clean the content to remove image links
                            link_content = self._clean_markdown_content(link_content)

                            # Then apply tag filtering if specified
                            if (include_tags or exclude_tags):
                                link_content = self._filter_content(link_content, include_tags, exclude_tags)

                        # Add article to combined content
                        combined_content += f"## {link_result.get('title', f'Story {i+1}')}\n\n"
                        combined_content += f"Source: {link_result.get('source_url', link)}\n\n"
                        combined_content += link_content + "\n\n---\n\n"
                        processed_links += 1

                    except Exception as e:
                        logger.error(f"Error processing trending story {link}: {str(e)}")

                if processed_links > 0:
                    # Update the result with the combined content
                    result["markdown_content"] = combined_content
                    result["title"] = f"Trending Stories: {result.get('title', 'Source')} ({processed_links} articles)"
                    result["processed_links"] = processed_links

                    logger.info(f"Processed {processed_links} trending stories from {url}")
                    return result

        # If no trending stories or processing failed, clean and apply tag filtering if needed
        if result.get("markdown_content"):
            # First clean the content to remove image links
            result["markdown_content"] = self._clean_markdown_content(result["markdown_content"])

            # Then apply tag filtering if specified
            if (include_tags or exclude_tags):
                result["markdown_content"] = self._filter_content(result["markdown_content"], include_tags, exclude_tags)

        return result

    async def _check_if_rss_feed(self, url: str) -> tuple[bool, Optional[str]]:
        """Check if a URL is an RSS feed by making a direct request.

        Args:
            url: URL to check

        Returns:
            Tuple of (is_rss, feed_data)
        """
        # First check URL pattern
        url_lower = url.lower()
        if any(pattern in url_lower for pattern in ['/rss', '/feed', '/xml', '.rss', '.xml', 'feed=', 'rss=']):
            logger.info(f"URL {url} likely an RSS feed based on URL pattern")

        try:
            async with aiohttp.ClientSession() as session:
                logger.info(f"Making direct request to check if {url} is an RSS feed")
                async with session.get(url, timeout=10) as response:
                    if response.status != 200:
                        logger.warning(f"Failed to fetch RSS feed from {url}: {response.status}")
                        return False, None

                    # Check content type
                    content_type = response.headers.get('Content-Type', '').lower()
                    if any(ct in content_type for ct in ['application/rss+xml', 'application/xml', 'text/xml']):
                        logger.info(f"URL {url} confirmed as RSS feed based on Content-Type: {content_type}")
                        feed_data = await response.text()
                        return True, feed_data

                    # If content type doesn't confirm it, check the content
                    feed_data = await response.text()
                    content_start = feed_data.strip().lower()

                    if (content_start.startswith("<?xml") and
                        ("<rss" in content_start[:1000] or "<feed" in content_start[:1000] or "<channel>" in content_start[:1000])):
                        logger.info(f"URL {url} confirmed as RSS feed based on content inspection")
                        return True, feed_data

                    return False, None
        except Exception as e:
            logger.error(f"Error checking if {url} is an RSS feed: {str(e)}")
            return False, None

    async def _process_rss_feed(self, url: str, feed_data: Optional[str], include_tags: Optional[str], exclude_tags: Optional[str]) -> Dict[str, Any]:
        """Process an RSS feed by extracting and processing links directly.

        Args:
            url: Original RSS feed URL
            feed_data: RSS feed content if already fetched, otherwise None
            include_tags: Tags to include when processing article content
            exclude_tags: Tags to exclude when processing article content

        Returns:
            Dictionary with processed feed content
        """
        try:
            # If feed data wasn't provided, fetch it
            if not feed_data:
                async with aiohttp.ClientSession() as session:
                    logger.info(f"Fetching RSS feed content from {url}")
                    async with session.get(url, timeout=10) as response:
                        if response.status != 200:
                            logger.error(f"Failed to fetch RSS feed from {url}: {response.status}")
                            return {
                                "title": "RSS Feed",
                                "source_url": url,
                                "markdown_content": f"Error fetching RSS feed: HTTP {response.status}",
                                "error": f"HTTP error: {response.status}"
                            }
                        feed_data = await response.text()

            # Extract feed title
            feed_title = "RSS Feed"
            title_match = re.search(r'<title>\s*(?:<!\[CDATA\[\s*)?(.*?)(?:\s*\]\]>)?\s*</title>', feed_data, re.IGNORECASE)
            if title_match:
                feed_title = title_match.group(1).strip()

            # Extract links from the feed
            links = self._extract_links_from_xml(feed_data)

            if not links:
                logger.warning(f"No links found in RSS feed {url}")
                return {
                    "title": feed_title,
                    "source_url": url,
                    "markdown_content": f"# {feed_title}\n\nNo articles found in the feed.",
                    "error": None
                }

            logger.info(f"Found {len(links)} links in RSS feed {url}: {links[:5]}{'...' if len(links) > 5 else ''}")

            # Process each link and combine the results
            combined_content = f"# {feed_title}\n\n"
            combined_content += f"Source: {url}\n\n"
            combined_content += f"Found {len(links)} articles in the feed.\n\n"

            # Process up to 10 links to avoid overloading
            max_links = min(10, len(links))
            processed_links = 0

            for i, link in enumerate(links[:max_links]):
                try:
                    logger.info(f"Processing feed link {i+1}/{max_links}: {link}")

                    # Make sure the link is a valid URL
                    if not link.startswith('http://') and not link.startswith('https://'):
                        if link.startswith('/'):
                            # Relative URL, try to construct full URL
                            parsed_url = urlparse(url)
                            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
                            link = urljoin(base_url, link)
                        else:
                            # Try to prepend https://
                            link = 'https://' + link

                    logger.info(f"Fetching content from link: {link}")
                    link_result = await self._fetch_raw_content(link)

                    if link_result.get("error"):
                        logger.warning(f"Error fetching content from link {link}: {link_result.get('error')}")
                        continue

                    # Get the content and clean it
                    link_content = link_result.get("markdown_content", "")
                    if link_content:
                        # First clean the content to remove image links
                        link_content = self._clean_markdown_content(link_content)

                        # Then apply tag filtering if specified
                        if (include_tags or exclude_tags):
                            link_content = self._filter_content(link_content, include_tags, exclude_tags)

                    # Add article to combined content
                    combined_content += f"## {link_result.get('title', f'Article {i+1}')}\n\n"
                    combined_content += f"Source: {link_result.get('source_url', link)}\n\n"
                    combined_content += link_content + "\n\n---\n\n"
                    processed_links += 1

                except Exception as e:
                    logger.error(f"Error processing link {link}: {str(e)}")

            return {
                "title": f"Feed: {feed_title} ({processed_links} articles)",
                "source_url": url,
                "markdown_content": combined_content,
                "processed_links": processed_links,
                "error": None
            }

        except Exception as e:
            logger.exception(f"Error processing RSS feed {url}: {str(e)}")
            return {
                "title": "RSS Feed",
                "source_url": url,
                "markdown_content": f"Error processing RSS feed: {str(e)}",
                "error": str(e)
            }

    async def _fetch_raw_content(self, url: str) -> Dict[str, Any]:
        """Fetch raw content from a URL using Jina AI without any processing.

        Args:
            url: URL to fetch content from

        Returns:
            Dictionary containing title, source URL, and markdown content
        """
        # Ensure URL is properly formatted
        if url.startswith('http://') or url.startswith('https://'):
            jina_url = f"{self.base_url}{url}"
        else:
            jina_url = f"{self.base_url}https://{url}"

        try:
            async with aiohttp.ClientSession() as session:
                logger.info(f"Fetching content from {jina_url}")
                async with session.get(jina_url) as response:
                    if response.status != 200:
                        logger.error(f"Failed to fetch content from {jina_url}: {response.status}")
                        return {
                            "title": None,
                            "source_url": url,
                            "markdown_content": None,
                            "error": f"HTTP error: {response.status}"
                        }

                    # Check content type
                    content_type = response.headers.get('Content-Type', '')

                    if 'application/json' in content_type:
                        # Handle JSON response
                        data = await response.json()
                        markdown_content = data.get("markdown")

                        # Return raw content without filtering

                        return {
                            "title": data.get("title"),
                            "source_url": data.get("url", url),
                            "markdown_content": markdown_content,
                            "error": None
                        }
                    else:
                        # Handle text response
                        text = await response.text()
                        # Try to extract a title from the URL
                        title = url.split('/')[-1].replace('-', ' ').title()
                        if not title:
                            title = "Fetched Content"

                        # Return raw content without filtering

                        logger.info(f"Received text content from {jina_url} (not JSON)")
                        return {
                            "title": title,
                            "source_url": url,
                            "markdown_content": text,
                            "error": None
                        }
        except Exception as e:
            logger.exception(f"Error fetching content from {jina_url}: {str(e)}")
            return {
                "title": None,
                "source_url": url,
                "markdown_content": None,
                "error": str(e)
            }

    def _clean_markdown_content(self, content: str) -> str:
        """Clean markdown content by removing image links and other unwanted elements.

        Args:
            content: Markdown content to clean

        Returns:
            Cleaned content
        """
        if not content:
            return content

        # Remove image links in markdown format: ![alt text](image_url)
        content = re.sub(r'!\[.*?\]\(.*?\)', '', content)

        # Remove image links in HTML format: <img src="image_url" ... />
        content = re.sub(r'<img[^>]*>', '', content)

        # Remove markdown image links with reference style: [![alt text](image_url)](link_url)
        content = re.sub(r'\[!\[.*?\]\(.*?\)\]\(.*?\)', '', content)

        # Remove empty links: [](url) that often appear in trending sections
        content = re.sub(r'\[\]\(https?://[^\)]+\)\s*', '', content)

        # Remove empty lines that might be left after removing images
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)

        logger.info("Cleaned markdown content by removing image links")
        return content.strip()

    def _extract_trending_links(self, content: str) -> List[str]:
        """Extract links from trending stories or similar list sections.

        Args:
            content: Markdown content to extract links from

        Returns:
            List of extracted URLs
        """
        links = []

        # Check if content contains a trending stories section with the specific format
        trending_section_match = re.search(r'(?i)(trending|popular|latest|top|featured)\s+(stories|news|articles|posts|headlines)[\s\-]*\n', content)
        if trending_section_match:
            section_start = trending_section_match.start()
            section_title = content[section_start:trending_section_match.end()].strip()
            logger.info(f"Detected trending stories section '{section_title}' at position {section_start} in content")

            # Get the content after the trending stories header
            section_content = content[section_start:]

            # First try to extract links from list items with the specific format you provided
            # Format: *   [Title](URL)
            list_item_links = re.findall(r'\*\s+\[([^\]]+)\]\((https?://[^\)]+)\)', section_content)

            # If that doesn't work, try more general list formats
            if not list_item_links:
                # Look for patterns like: * [text](url) or - [text](url) or numbered lists
                list_item_links = re.findall(r'[\*\-\d+\.]+\s+\[([^\]]+)\]\((https?://[^\)]+)\)', section_content)

            if list_item_links:
                for text, url in list_item_links:
                    if text.strip():  # Only add links with actual text
                        links.append(url)
                logger.info(f"Extracted {len(links)} links from trending stories list items")
            else:
                # If no list items found, try to extract all links in the section
                all_links = re.findall(r'\[([^\]]+)\]\((https?://[^\)]+)\)', section_content)
                for text, url in all_links:
                    if text.strip():  # Only add links with actual text
                        links.append(url)
                logger.info(f"Extracted {len(links)} links from trending stories section")

            # Log the extracted links for debugging
            if links:
                logger.info(f"Extracted trending story links: {links[:5]}{'...' if len(links) > 5 else ''}")

        return links

    def _filter_content(self, content: str, include_tags: Optional[str] = None, exclude_tags: Optional[str] = None) -> str:
        """Filter HTML content based on include/exclude tags.

        Args:
            content: HTML or markdown content to filter
            include_tags: Comma-separated list of HTML tags to include (only these tags will be kept)
            exclude_tags: Comma-separated list of HTML tags to exclude (these tags will be removed)

        Returns:
            Filtered content
        """
        # If no filtering needed, return original content
        if not include_tags and not exclude_tags:
            return content

        try:
            # Parse content with BeautifulSoup
            soup = BeautifulSoup(content, 'html.parser')

            # Process exclude tags first
            if exclude_tags:
                tags_to_exclude = [tag.strip() for tag in exclude_tags.split(',')]
                for tag in tags_to_exclude:
                    for element in soup.find_all(tag):
                        element.decompose()

            # Process include tags
            if include_tags:
                tags_to_include = [tag.strip() for tag in include_tags.split(',')]

                # Create a new soup with only the specified tags
                new_soup = BeautifulSoup('', 'html.parser')

                # Find all elements with the specified tags and add them to the new soup
                for tag in tags_to_include:
                    for element in soup.find_all(tag):
                        new_soup.append(element)

                # Replace the original soup with the filtered one
                soup = new_soup

            # Convert back to string
            filtered_content = str(soup)

            # Log the filtering operation
            logger.info(f"Content filtered: include_tags={include_tags}, exclude_tags={exclude_tags}")

            return filtered_content
        except Exception as e:
            logger.error(f"Error filtering content: {str(e)}")
            # Return original content if filtering fails
            return content
