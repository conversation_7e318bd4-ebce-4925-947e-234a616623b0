import sqlite3
import os
import logging
from datetime import datetime
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_db_file():
    db_url = os.environ.get("DATABASE_URL")
    if db_url and db_url.startswith("sqlite:///"):
        path = db_url.replace("sqlite:///", "", 1)
        if path.startswith("/"):
            path = path[1:]
        return path
    return os.environ.get("DB_FILE", "data/database.sqlite")

DB_FILE = get_db_file()

def add_scheduled_jobs_table():
    """Add scheduled_jobs table to the database."""
    if not os.path.exists(DB_FILE):
        logger.error(f"Database file {DB_FILE} not found")
        return False

    try:
        # Connect to the database
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()

        # Check if scheduled_jobs table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='scheduled_jobs'")
        if cursor.fetchone() is None:
            logger.info("Creating scheduled_jobs table...")
            
            # Create scheduled_jobs table
            cursor.execute("""
            CREATE TABLE scheduled_jobs (
                id INTEGER PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                job_id VARCHAR(100) NOT NULL UNIQUE,
                category_id INTEGER NOT NULL,
                hour INTEGER NOT NULL,
                minute INTEGER NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_run TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE
            )
            """)
            
            conn.commit()
            logger.info("Scheduled jobs table created successfully")
        else:
            logger.info("Scheduled jobs table already exists")
            
        conn.close()
        return True
    except Exception as e:
        logger.exception(f"Error adding scheduled_jobs table: {str(e)}")
        return False

if __name__ == "__main__":
    add_scheduled_jobs_table()
