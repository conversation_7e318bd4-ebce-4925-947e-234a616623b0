import logging
import os
from typing import List, Dict, Any, Optional

import faiss
import numpy as np
from sentence_transformers import SentenceTransformer

from app.config import settings

# Set up logger
logger = logging.getLogger(__name__)


class EmbeddingService:
    """Service for generating and storing vector embeddings."""

    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """Initialize the embedding service.

        Args:
            model_name: Name of the sentence transformer model to use
        """
        self.model_name = model_name
        self.model = SentenceTransformer(model_name)
        self.vector_store_path = settings.vector_store_dir
        self.index_path = os.path.join(self.vector_store_path, "faiss_index.bin")
        self.metadata_path = os.path.join(self.vector_store_path, "metadata.npy")

        # Initialize or load the index
        self._init_index()

    def _init_index(self):
        """Initialize or load the FAISS index."""
        try:
            if os.path.exists(self.index_path) and os.path.exists(self.metadata_path):
                # Load existing index and metadata
                self.index = faiss.read_index(self.index_path)
                self.metadata = np.load(self.metadata_path, allow_pickle=True).item()
                logger.info(f"Loaded existing FAISS index with {self.index.ntotal} vectors")
            else:
                # Create new index and metadata
                dimension = self.model.get_sentence_embedding_dimension()
                self.index = faiss.IndexFlatL2(dimension)
                self.metadata = {"ids": [], "result_ids": [], "titles": [], "sources": []}
                logger.info(f"Created new FAISS index with dimension {dimension}")
        except Exception as e:
            logger.exception(f"Error initializing FAISS index: {str(e)}")
            # Create new index as fallback
            dimension = self.model.get_sentence_embedding_dimension()
            self.index = faiss.IndexFlatL2(dimension)
            self.metadata = {"ids": [], "result_ids": [], "titles": [], "sources": []}

    def _save_index(self):
        """Save the FAISS index and metadata to disk."""
        try:
            os.makedirs(self.vector_store_path, exist_ok=True)
            faiss.write_index(self.index, self.index_path)
            np.save(self.metadata_path, self.metadata)
            logger.info(f"Saved FAISS index with {self.index.ntotal} vectors")
        except Exception as e:
            logger.exception(f"Error saving FAISS index: {str(e)}")

    def generate_embedding(self, text: str) -> np.ndarray:
        """Generate an embedding for a text.

        Args:
            text: Text to generate embedding for

        Returns:
            Embedding vector as numpy array
        """
        try:
            return self.model.encode(text)
        except Exception as e:
            logger.exception(f"Error generating embedding: {str(e)}")
            # Return a zero vector as fallback
            return np.zeros(self.model.get_sentence_embedding_dimension())

    def add_to_index(self, result_id: int, text: str, title: str, source: str) -> bool:
        """Add a text embedding to the index.

        Args:
            result_id: ID of the result in the database
            text: Text to generate embedding for
            title: Title of the content
            source: Source URL of the content

        Returns:
            True if successful, False otherwise
        """
        try:
            # Generate embedding
            embedding = self.generate_embedding(text)

            # Add to index
            index_id = self.index.ntotal
            self.index.add(np.array([embedding], dtype=np.float32))

            # Add metadata
            self.metadata["ids"].append(index_id)
            self.metadata["result_ids"].append(result_id)
            self.metadata["titles"].append(title)
            self.metadata["sources"].append(source)

            # Save index
            self._save_index()

            logger.info(f"Added embedding for result ID {result_id} to index")
            return True
        except Exception as e:
            logger.exception(f"Error adding embedding to index: {str(e)}")
            return False

    def search(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """Search for similar content.

        Args:
            query: Query text
            k: Number of results to return

        Returns:
            List of dictionaries containing search results
        """
        try:
            # Generate query embedding
            query_embedding = self.generate_embedding(query)

            # Search index
            distances, indices = self.index.search(np.array([query_embedding], dtype=np.float32), k)

            # Prepare results
            results = []
            for i, idx in enumerate(indices[0]):
                if idx != -1 and idx < len(self.metadata["ids"]):
                    position = self.metadata["ids"].index(idx)
                    results.append({
                        "result_id": self.metadata["result_ids"][position],
                        "title": self.metadata["titles"][position],
                        "source": self.metadata["sources"][position],
                        "distance": float(distances[0][i])
                    })

            logger.info(f"Search for '{query}' returned {len(results)} results")
            return results
        except Exception as e:
            logger.exception(f"Error searching index: {str(e)}")
            return []

    def remove_from_index(self, result_id: int) -> bool:
        """Remove a result from the index.

        Args:
            result_id: ID of the result to remove

        Returns:
            True if successful, False otherwise
        """
        try:
            # Find positions to remove
            positions_to_remove = [i for i, rid in enumerate(self.metadata["result_ids"]) if rid == result_id]
            if not positions_to_remove:
                logger.warning(f"Result ID {result_id} not found in index")
                return False

            # Create a new index and metadata
            dimension = self.model.get_sentence_embedding_dimension()
            new_index = faiss.IndexFlatL2(dimension)
            new_metadata = {"ids": [], "result_ids": [], "titles": [], "sources": []}

            # Copy data excluding the positions to remove
            for i in range(self.index.ntotal):
                if i not in positions_to_remove:
                    # Get the embedding vector
                    vector = faiss.vector_to_array(self.index.get_vector(i)).reshape(1, -1)

                    # Add to new index
                    new_id = new_index.ntotal
                    new_index.add(vector)

                    # Find position in metadata
                    position = self.metadata["ids"].index(i)

                    # Add metadata
                    new_metadata["ids"].append(new_id)
                    new_metadata["result_ids"].append(self.metadata["result_ids"][position])
                    new_metadata["titles"].append(self.metadata["titles"][position])
                    new_metadata["sources"].append(self.metadata["sources"][position])

            # Replace old index and metadata
            self.index = new_index
            self.metadata = new_metadata

            # Save index
            self._save_index()

            logger.info(f"Removed result ID {result_id} from index")
            return True
        except Exception as e:
            logger.exception(f"Error removing from index: {str(e)}")
            return False
