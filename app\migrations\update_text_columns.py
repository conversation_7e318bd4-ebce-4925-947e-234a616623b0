"""
Migration script to update text column sizes in the database.
This script should be run manually to update the database schema.
"""

import sqlite3
import os
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database path - search for the database file
def find_database():
    """Find the database file by searching common locations."""
    possible_paths = [
        Path(__file__).parent.parent.parent / "news.db",
        Path(__file__).parent.parent.parent / "app.db",
        Path(__file__).parent.parent.parent / "database.db",
        Path(__file__).parent.parent.parent / "data.db",
        Path(__file__).parent.parent.parent / "news-and-more.db",
        Path.cwd() / "news.db",
        Path.cwd() / "app.db",
        Path.cwd() / "database.db",
        Path.cwd() / "data.db",
        Path.cwd() / "news-and-more.db",
    ]

    # Check each possible path
    for path in possible_paths:
        if path.exists():
            logger.info(f"Found database at {path}")
            return path

    # If no database found, ask the user
    logger.info("Database not found in common locations. Please enter the path:")
    user_path = input("Database path: ")
    return Path(user_path)

DB_PATH = find_database()

def run_migration():
    """Run the migration to update text column sizes."""
    if not os.path.exists(DB_PATH):
        logger.error(f"Database file not found at {DB_PATH}")
        return False

    logger.info(f"Running migration on database: {DB_PATH}")

    try:
        # Connect to the database
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # SQLite doesn't enforce column sizes for TEXT columns, but we'll recreate the tables
        # with the new schema to ensure compatibility with the SQLAlchemy models

        # 1. Create temporary tables with the new schema
        logger.info("Creating temporary tables with new schema...")

        # Create temp table for results
        cursor.execute("""
        CREATE TABLE results_new (
            id INTEGER PRIMARY KEY,
            url_id INTEGER NOT NULL,
            title VARCHAR(512),
            source_url VARCHAR(2048),
            markdown_content TEXT,
            llm_analysis TEXT,
            llm_type VARCHAR(50) NOT NULL,
            llm_model VARCHAR(100) NOT NULL,
            processed_at TIMESTAMP,
            has_embedding BOOLEAN DEFAULT 0,
            FOREIGN KEY(url_id) REFERENCES urls(id)
        )
        """)

        # Create temp table for aggregated_results
        cursor.execute("""
        CREATE TABLE aggregated_results_new (
            id INTEGER PRIMARY KEY,
            title VARCHAR(512) NOT NULL,
            source_count INTEGER NOT NULL,
            aggregated_analysis TEXT NOT NULL,
            llm_type VARCHAR(50) NOT NULL,
            llm_model VARCHAR(100) NOT NULL,
            processed_at TIMESTAMP,
            result_ids VARCHAR(1024) NOT NULL
        )
        """)

        # 2. Copy data from old tables to new tables
        logger.info("Copying data to new tables...")
        cursor.execute("INSERT INTO results_new SELECT * FROM results")
        cursor.execute("INSERT INTO aggregated_results_new SELECT * FROM aggregated_results")

        # 3. Drop old tables
        logger.info("Dropping old tables...")
        cursor.execute("DROP TABLE results")
        cursor.execute("DROP TABLE aggregated_results")

        # 4. Rename new tables to original names
        logger.info("Renaming new tables...")
        cursor.execute("ALTER TABLE results_new RENAME TO results")
        cursor.execute("ALTER TABLE aggregated_results_new RENAME TO aggregated_results")

        # 5. Recreate indexes
        logger.info("Recreating indexes...")
        cursor.execute("CREATE INDEX ix_results_id ON results (id)")
        cursor.execute("CREATE INDEX ix_aggregated_results_id ON aggregated_results (id)")

        # Commit changes
        conn.commit()
        logger.info("Migration completed successfully")
        return True

    except Exception as e:
        logger.error(f"Error during migration: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    run_migration()
