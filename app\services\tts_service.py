import logging
import os
from enum import Enum
from typing import Optional

import pyttsx3
from gtts import gTTS

from app.config import settings

# Set up logger
logger = logging.getLogger(__name__)


class TTSEngine(str, Enum):
    """Enum for TTS engines."""
    PYTTSX3 = "pyttsx3"  # Offline
    GTTS = "gtts"  # Online (Google)


class TTSService:
    """Service for text-to-speech functionality."""

    def __init__(self, engine: TTSEngine = TTSEngine.PYTTSX3):
        """Initialize the TTS service.

        Args:
            engine: TTS engine to use
        """
        self.engine_type = engine
        self.output_dir = settings.exports_dir
        os.makedirs(self.output_dir, exist_ok=True)

    def text_to_speech(self, text: str, output_file: Optional[str] = None) -> str:
        """Convert text to speech.

        Args:
            text: Text to convert to speech
            output_file: Optional output file path

        Returns:
            Path to the generated audio file
        """
        if not text:
            logger.warning("No text provided for TTS")
            return ""

        # Generate default output file name if not provided
        if not output_file:
            output_file = os.path.join(self.output_dir, "tts_output.mp3")

        try:
            if self.engine_type == TTSEngine.PYTTSX3:
                return self._pyttsx3_tts(text, output_file)
            else:  # TTSEngine.GTTS
                return self._gtts_tts(text, output_file)
        except Exception as e:
            logger.exception(f"Error in TTS conversion: {str(e)}")
            return ""

    def _pyttsx3_tts(self, text: str, output_file: str) -> str:
        """Convert text to speech using pyttsx3 (offline).

        Args:
            text: Text to convert to speech
            output_file: Output file path

        Returns:
            Path to the generated audio file
        """
        try:
            # Ensure output file has .mp3 extension
            if not output_file.endswith(".mp3"):
                output_file = f"{output_file}.mp3"

            # Initialize engine
            engine = pyttsx3.init()

            # Set properties
            engine.setProperty("rate", 150)  # Speed
            engine.setProperty("volume", 0.9)  # Volume (0.0 to 1.0)

            # Save to file
            engine.save_to_file(text, output_file)
            engine.runAndWait()

            logger.info(f"Generated TTS audio using pyttsx3: {output_file}")
            return output_file
        except Exception as e:
            logger.exception(f"Error in pyttsx3 TTS conversion: {str(e)}")
            return ""

    def _gtts_tts(self, text: str, output_file: str) -> str:
        """Convert text to speech using gTTS (Google TTS, online).

        Args:
            text: Text to convert to speech
            output_file: Output file path

        Returns:
            Path to the generated audio file
        """
        try:
            # Ensure output file has .mp3 extension
            if not output_file.endswith(".mp3"):
                output_file = f"{output_file}.mp3"

            # Initialize gTTS
            tts = gTTS(text=text, lang="en", slow=False)

            # Save to file
            tts.save(output_file)

            logger.info(f"Generated TTS audio using gTTS: {output_file}")
            return output_file
        except Exception as e:
            logger.exception(f"Error in gTTS TTS conversion: {str(e)}")
            return ""
