# News and More

A Python web application for fetching web content using Jina AI, analyzing it with configurable LLMs (Ollama or Google Gemini), and managing the results. The application can process individual URLs, RSS feeds, and even extract and process "trending stories" sections from websites.

![News and More Screenshot](app/static/img/screenshot.png)

## Features

- **Web Interface**: Modern UI for managing URLs and viewing processed results
- **Data Fetching**: Fetch web content using Jina AI with configurable HTML tag filtering
- **Link Processing**: Automatically extract and process links from RSS feeds and "trending stories" sections
- **Content Analysis**: Process content with Ollama (local) or Google Gemini (cloud) LLMs
- **Aggregation**: Combine multiple analyses into comprehensive reports with bilingual output
- **Data Storage**: Store results in SQLite database with efficient querying
- **Results Management**: Display, search, export, and manage results
- **Vector Embedding**: Create and search vector embeddings of analysis results
- **Scheduling**: Automate processing on a configurable schedule
- **Text-to-Speech**: Convert analysis results to audio with multiple TTS engines

## Installation

### Prerequisites

- Python 3.9+ installed
- For Ollama integration: [Ollama](https://ollama.ai/) installed and running
- For Gemini integration: Google Gemini API key

### Setup

1. **Clone the repository:**
   ```bash
   git clone https://github.com/yourusername/news-and-more.git
   cd news-and-more
   ```

2. **Create a virtual environment:**
   ```bash
   # On Windows
   python -m venv .venv
   .venv\Scripts\activate

   # On macOS/Linux
   python -m venv .venv
   source .venv/bin/activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Create environment file:**
   Create a `.env` file in the project root with the following content:
   ```
   APP_NAME="News and More"
   DEBUG=true
   LOG_LEVEL=INFO

   # Ollama settings
   OLLAMA_BASE_URL=http://localhost:11434
   OLLAMA_MODEL=llama3

   # Gemini settings
   GEMINI_API_KEY=your_gemini_api_key_here
   GEMINI_MODEL=gemini-1.5-pro

   # Default LLM to use
   DEFAULT_LLM=ollama

   # Content chunking settings
   CHUNK_SIZE=8000
   CHUNK_OVERLAP=200

   # Scheduler settings
   SCHEDULER_ENABLED=true
   SCHEDULER_HOUR=8
   SCHEDULER_MINUTE=0
   ```

## Usage

### Starting the Application

1. **Run the application:**
   ```bash
   python -m app.main
   ```

2. **Access the web interface:**
   Open your browser and navigate to `http://localhost:8181`

### Managing URLs

1. **Add a URL:**
   - Click "Add URL" on the main page
   - Enter the URL and configure options:
     - **Include Tags**: Comma-separated list of HTML tags to include (e.g., `article,p,h1,h2,h3`)
     - **Exclude Tags**: Comma-separated list of HTML tags to exclude (e.g., `nav,footer,aside`)
     - **Process Links**: Enable to extract and process links from RSS feeds or "trending stories" sections

2. **Test a URL:**
   - Click "Test URL" to preview content fetching without saving
   - Verify tag filtering and link processing options

3. **Process URLs:**
   - Click "Process" next to a URL to analyze it individually
   - Select multiple URLs and click "Process Selected" to analyze them as a batch
   - Click "Process All" to analyze all URLs

### Viewing Results

1. **Individual Results:**
   - Go to the "Results" page and click on the "Individual Results" tab
   - Filter results by URL using the dropdown
   - Click the eye icon to view full analysis details

2. **Aggregated Results:**
   - Go to the "Results" page and click on the "Aggregated Results" tab
   - View combined analyses from batch processing or scheduled jobs
   - Click the eye icon to view the comprehensive analysis and source list

3. **Search Results:**
   - Use the search box on the Results page to find specific content
   - Results are ranked by semantic similarity using vector embeddings

### Additional Features

1. **Text-to-Speech:**
   - Click the "Text-to-Speech" button on any result to generate audio
   - Choose between offline (pyttsx3) or online (Google TTS) engines

2. **Export Results:**
   - Export individual or multiple results in various formats (JSON, CSV, TXT, Markdown)

3. **Vector Embeddings:**
   - Generate embeddings for results to enable semantic search
   - Click "Generate Embedding" on a result detail page

## Scheduler Configuration

1. Go to the "Settings" page
2. Enable the scheduler and set the desired time (24-hour format)
3. The scheduler will automatically process all active URLs at the specified time
4. Processed results will be stored in the database and aggregated

## Content Chunking Strategy

The application implements a sophisticated content chunking strategy to handle large documents that exceed LLM context windows.

### Chunking Logic

#### Configuration Parameters

- **CHUNK_SIZE**: Maximum size of each chunk in characters (default: 8000)
- **CHUNK_OVERLAP**: Number of overlapping characters between chunks (default: 200)
- **For Gemini**: Larger chunk sizes (up to 1,000,000) can be used due to Gemini's larger context window

#### Chunking Process

1. **Text Preprocessing**:
   - Content is first cleaned to remove excessive whitespace
   - Special characters and formatting are normalized

2. **Intelligent Splitting**:
   - Text is split at natural boundaries (paragraphs, sentences) when possible
   - The system avoids splitting in the middle of sentences or paragraphs
   - Headers and section titles are kept with their content when possible

3. **Overlap Management**:
   - Each chunk overlaps with the next by the specified number of characters
   - This ensures context continuity between chunks
   - Overlap boundaries are adjusted to avoid splitting words or sentences

4. **LLM-Specific Handling**:
   - For Ollama: Content is always chunked according to the specified parameters
   - For Gemini: If content is smaller than 800,000 characters, chunking is skipped entirely

### Processing Strategy

1. **Sequential Processing**:
   - Each chunk is processed in sequence by the LLM
   - The prompt is applied to each chunk individually

2. **Result Aggregation**:
   - Results from all chunks are combined into a single coherent analysis
   - For multi-chunk documents, the LLM synthesizes the individual analyses

3. **Optimization**:
   - Empty or redundant chunks are filtered out
   - Chunks with minimal content are merged with adjacent chunks
   - The system tracks token counts to optimize for LLM context windows

### Example

For a 25,000 character document with default settings:

1. Document is split into 4 chunks of approximately 8,000 characters each
2. Each chunk overlaps with the next by 200 characters
3. Each chunk is processed by the LLM separately
4. The 4 individual analyses are combined into a single coherent result

For the same document using Gemini:

1. Document is processed as a single chunk (no splitting)
2. This leverages Gemini's larger context window for better coherence

### Benefits

- **Handles Documents of Any Size**: No practical limit on document length
- **Maintains Context**: Overlap ensures continuity between chunks
- **Optimized for Different LLMs**: Adapts strategy based on the LLM's capabilities
- **Preserves Document Structure**: Respects natural text boundaries

## LLM Prompts

The application uses the following prompts for content analysis:

### Individual Content Analysis Prompt

```
请分析以下内容，并生成一份**详尽且全面的摘要**。

这份摘要需要在**详细程度**和**简洁性**之间取得良好平衡。它的目标是：
1.  **足够详细**：能够准确、透彻地传达原文的核心信息、主要论点、关键支撑细节和结论。
2.  **独立理解**：让读者**无需回顾原文**就能对内容有扎实和全面的理解。
3.  **保持摘要性质**：避免变成对原文的简单复述或冗长的解释，抓住精髓。
4.  **避免过于简略**：确保包含所有理解原文主旨所必需的关键信息点。

请确保你的摘要涵盖以下方面：
* 清晰地阐述内容的主题、核心议题或目的。
* 详细梳理主要论点、观点或内容的各个关键部分，并包含必要的支撑信息、证据或解释。
* 提及原文中呈现的任何重要发现、数据或具有代表性的示例。
* 总结作者得出的主要结论或讨论的潜在影响/意义。
* 如果原文中有特别精辟或高度概括核心思想的引言（名言警句），酌情引用1-2句最关键的。

请将摘要组织得逻辑清晰、易于理解。
**语言要求**：如果原始内容是中文，请**完全使用中文**提供摘要。不要将其翻译成英文。

原始内容 (CONTENT):
{content}

详尽摘要 (COMPREHENSIVE SUMMARY):
```

### Aggregation Analysis Prompt

```
You are an expert analyst tasked with reviewing and synthesizing multiple source summaries into a comprehensive report.

Below are summaries from {source_count} different sources that have already been analyzed individually. Your task is to create a detailed report that fulfills the following requirements in two language versions:

**Part 1: Original Language Output**

1.  **Present Original Summaries:**
    * Reproduce the **full, unaltered content** of all the provided source summaries below.
    * You may reorder the summaries for better logical flow if necessary, but **you must not shorten, summarize further, or delete any part of the original text**. Clearly label this section (e.g., "Original Source Summaries").

2.  **Provide Comprehensive Analysis:**
    * **Following** the presentation of the original summaries, provide a unified synthesis and analysis based *on* them.
    * This analysis should:
        * Identify the most important themes, trends, and insights emerging across **all** provided summaries.
        * Highlight specific areas of consensus and disagreement between the sources, referencing points from the summaries.
        * Offer a balanced perspective that acknowledges the different viewpoints or findings presented.
        * Explicitly note the most valuable or unique information contributed by each source summary.
        * Organize the analysis section in a clear, logical structure (e.g., using headings for themes, consensus/disagreement, etc.).
        * Draw meaningful conclusions and discuss potential implications based on the collective information from the summaries.
    * Clearly label this section (e.g., "Comprehensive Analysis").

3.  **Language:**
    * If the original source summaries (`{sources}`) are in a language other than English, provide this entire Part 1 (Original Summaries + Comprehensive Analysis) in **that same language**.
    * If the sources are in English, provide Part 1 in English.

**Part 2: Simplified Chinese Translation**

1.  **Translate Entire Output:**
    * Provide a **complete and accurate translation** of the *entire* Part 1 (including the reproduced Original Summaries and your Comprehensive Analysis) into **Simplified Chinese (简体中文)**.
    * Maintain the structure established in Part 1 (e.g., keep the summaries separate from the analysis).
    * Clearly label this section (e.g., "简体中文翻译 / Simplified Chinese Translation").

**Input Sources:**

SOURCES:
{sources}

**Output Structure:**

Please structure your final response clearly, first presenting Part 1 (Original Language: Original Summaries + Comprehensive Analysis) and then Part 2 (Simplified Chinese Translation of Part 1).
```

## Project Structure

```
news-and-more/
├── app/                    # Main application package
│   ├── api/                # API endpoints
│   ├── services/           # Business logic services
│   ├── static/             # Static files (CSS, JS, images)
│   ├── templates/          # HTML templates
│   ├── config.py           # Configuration settings
│   ├── database.py         # Database models and session
│   └── main.py             # Application entry point
├── logs/                   # Log files
├── .env                    # Environment variables
├── .gitignore              # Git ignore file
├── requirements.txt        # Python dependencies
└── README.md               # This file
```

## Troubleshooting

### Common Issues

1. **Ollama Connection Error**:
   - Ensure Ollama is running (`ollama serve`)
   - Check OLLAMA_BASE_URL in .env file

2. **Gemini API Key Error**:
   - Verify your Gemini API key is correct
   - Check for quota limitations

3. **Database Errors**:
   - If database becomes corrupted, delete the .db file and restart the application

4. **Scheduler Not Running**:
   - Check if SCHEDULER_ENABLED is set to true
   - Verify the time settings (SCHEDULER_HOUR, SCHEDULER_MINUTE)

### Logs

Check the logs directory for detailed application logs. The log level can be configured in the .env file or through the settings page.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

MIT
