import logging
import json
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session

from app.database import URL, Result, AggregatedResult, Category

# Set up logger
logger = logging.getLogger(__name__)


class StorageService:
    """Service for database operations."""

    def __init__(self, db: Session):
        """Initialize the storage service.

        Args:
            db: Database session
        """
        self.db = db

    # URL operations
    def get_urls(self, active_only: bool = True) -> List[URL]:
        """Get all URLs from the database.

        Args:
            active_only: Only return active URLs

        Returns:
            List of URL objects
        """
        query = self.db.query(URL)
        if active_only:
            query = query.filter(URL.is_active == True)
        return query.order_by(URL.created_at.desc()).all()

    def get_url_by_id(self, url_id: int) -> Optional[URL]:
        """Get a URL by ID.

        Args:
            url_id: ID of the URL

        Returns:
            URL object or None if not found
        """
        return self.db.query(URL).filter(URL.id == url_id).first()

    def get_url_by_url(self, url: str) -> Optional[URL]:
        """Get a URL by URL string.

        Args:
            url: URL string

        Returns:
            URL object or None if not found
        """
        return self.db.query(URL).filter(URL.url == url).first()

    def create_url(self, url: str, include_tags: Optional[str] = None, exclude_tags: Optional[str] = None,
                process_links: bool = False, category_ids: Optional[List[int]] = None) -> URL:
        """Create a new URL.

        Args:
            url: URL string
            include_tags: Comma-separated list of HTML tags to include
            exclude_tags: Comma-separated list of HTML tags to exclude
            process_links: Whether to process links found in feed content
            category_ids: List of category IDs to associate with the URL

        Returns:
            Created URL object
        """
        db_url = URL(
            url=url,
            include_tags=include_tags,
            exclude_tags=exclude_tags,
            process_links=process_links
        )

        # Add categories if provided
        if category_ids:
            categories = self.db.query(Category).filter(Category.id.in_(category_ids)).all()
            db_url.categories = categories

        self.db.add(db_url)
        self.db.commit()
        self.db.refresh(db_url)
        logger.info(f"Created URL: {url} with include_tags={include_tags}, exclude_tags={exclude_tags}, process_links={process_links}, categories={len(db_url.categories) if db_url.categories else 0}")
        return db_url

    def update_url(self, url_id: int, data: Dict[str, Any]) -> Optional[URL]:
        """Update a URL.

        Args:
            url_id: ID of the URL
            data: Dictionary of fields to update

        Returns:
            Updated URL object or None if not found
        """
        db_url = self.get_url_by_id(url_id)
        if not db_url:
            return None

        # Handle category_ids separately
        category_ids = data.pop('category_ids', None)
        if category_ids is not None:
            categories = self.db.query(Category).filter(Category.id.in_(category_ids)).all()
            db_url.categories = categories

        # Update other fields
        for key, value in data.items():
            if hasattr(db_url, key):
                setattr(db_url, key, value)

        self.db.commit()
        self.db.refresh(db_url)
        logger.info(f"Updated URL: {db_url.url}")
        return db_url

    def delete_url(self, url_id: int) -> bool:
        """Delete a URL.

        Args:
            url_id: ID of the URL

        Returns:
            True if deleted, False if not found
        """
        db_url = self.get_url_by_id(url_id)
        if not db_url:
            return False

        self.db.delete(db_url)
        self.db.commit()
        logger.info(f"Deleted URL: {db_url.url}")
        return True

    def update_url_processed_time(self, url_id: int) -> Optional[URL]:
        """Update the last processed time of a URL.

        Args:
            url_id: ID of the URL

        Returns:
            Updated URL object or None if not found
        """
        return self.update_url(url_id, {"last_processed_at": datetime.now(timezone.utc)})

    # Result operations
    def get_results(self, url_id: Optional[int] = None) -> List[Result]:
        """Get results from the database.

        Args:
            url_id: Optional URL ID to filter by

        Returns:
            List of Result objects
        """
        query = self.db.query(Result)
        if url_id is not None:
            query = query.filter(Result.url_id == url_id)
        return query.order_by(Result.processed_at.desc()).all()

    def get_result_by_id(self, result_id: int) -> Optional[Result]:
        """Get a result by ID.

        Args:
            result_id: ID of the result

        Returns:
            Result object or None if not found
        """
        return self.db.query(Result).filter(Result.id == result_id).first()

    def get_latest_result_by_url_id(self, url_id: int) -> Optional[Result]:
        """Get the most recent result for a URL.

        Args:
            url_id: ID of the URL

        Returns:
            Most recent Result object or None if not found
        """
        return self.db.query(Result).filter(Result.url_id == url_id).order_by(Result.processed_at.desc()).first()

    def create_result(self, url_id: int, data: Dict[str, Any]) -> Result:
        """Create a new result.

        Args:
            url_id: ID of the URL
            data: Dictionary of result data

        Returns:
            Created Result object
        """
        db_result = Result(url_id=url_id, **data)
        self.db.add(db_result)
        self.db.commit()
        self.db.refresh(db_result)
        logger.info(f"Created result for URL ID: {url_id}")
        return db_result

    def update_result(self, result_id: int, data: Dict[str, Any]) -> Optional[Result]:
        """Update a result.

        Args:
            result_id: ID of the result
            data: Dictionary of fields to update

        Returns:
            Updated Result object or None if not found
        """
        db_result = self.get_result_by_id(result_id)
        if not db_result:
            return None

        for key, value in data.items():
            if hasattr(db_result, key):
                setattr(db_result, key, value)

        self.db.commit()
        self.db.refresh(db_result)
        logger.info(f"Updated result ID: {result_id}")
        return db_result

    def delete_result(self, result_id: int) -> bool:
        """Delete a result.

        Args:
            result_id: ID of the result

        Returns:
            True if deleted, False if not found
        """
        db_result = self.get_result_by_id(result_id)
        if not db_result:
            return False

        self.db.delete(db_result)
        self.db.commit()
        logger.info(f"Deleted result ID: {result_id}")
        return True

    def update_embedding_status(self, result_id: int, has_embedding: bool) -> Optional[Result]:
        """Update the embedding status of a result.

        Args:
            result_id: ID of the result
            has_embedding: Whether the result has an embedding

        Returns:
            Updated Result object or None if not found
        """
        return self.update_result(result_id, {"has_embedding": has_embedding})

    # Aggregated Result operations
    def get_aggregated_results(self) -> List[AggregatedResult]:
        """Get all aggregated results.

        Returns:
            List of AggregatedResult objects
        """
        return self.db.query(AggregatedResult).order_by(AggregatedResult.processed_at.desc()).all()

    def get_aggregated_result_by_id(self, aggregated_id: int) -> Optional[AggregatedResult]:
        """Get an aggregated result by ID.

        Args:
            aggregated_id: ID of the aggregated result

        Returns:
            AggregatedResult object or None if not found
        """
        return self.db.query(AggregatedResult).filter(AggregatedResult.id == aggregated_id).first()

    def create_aggregated_result(self, data: Dict[str, Any]) -> AggregatedResult:
        """Create a new aggregated result.

        Args:
            data: Dictionary of aggregated result data

        Returns:
            Created AggregatedResult object
        """
        # Convert result_ids list to JSON string if it's a list
        if "result_ids" in data and isinstance(data["result_ids"], list):
            data["result_ids"] = json.dumps(data["result_ids"])

        db_aggregated = AggregatedResult(**data)
        self.db.add(db_aggregated)
        self.db.commit()
        self.db.refresh(db_aggregated)
        logger.info(f"Created aggregated result with {db_aggregated.source_count} sources")
        return db_aggregated

    def delete_aggregated_result(self, aggregated_id: int) -> bool:
        """Delete an aggregated result.

        Args:
            aggregated_id: ID of the aggregated result

        Returns:
            True if deleted, False if not found
        """
        db_aggregated = self.get_aggregated_result_by_id(aggregated_id)
        if not db_aggregated:
            return False

        self.db.delete(db_aggregated)
        self.db.commit()
        logger.info(f"Deleted aggregated result ID: {aggregated_id}")
        return True

    def get_result_ids_from_aggregated(self, aggregated_id: int) -> List[int]:
        """Get the result IDs from an aggregated result.

        Args:
            aggregated_id: ID of the aggregated result

        Returns:
            List of result IDs
        """
        db_aggregated = self.get_aggregated_result_by_id(aggregated_id)
        if not db_aggregated or not db_aggregated.result_ids:
            return []

        try:
            return json.loads(db_aggregated.result_ids)
        except json.JSONDecodeError:
            logger.error(f"Failed to parse result_ids from aggregated result {aggregated_id}")
            return []

    # Category operations
    def get_categories(self) -> List[Category]:
        """Get all categories from the database.

        Returns:
            List of Category objects with URL count
        """
        categories = self.db.query(Category).order_by(Category.name).all()

        # Add URL count to each category
        for category in categories:
            category.url_count = len(category.urls)

        return categories

    def get_category_by_id(self, category_id: int) -> Optional[Category]:
        """Get a category by ID.

        Args:
            category_id: ID of the category

        Returns:
            Category object or None if not found
        """
        category = self.db.query(Category).filter(Category.id == category_id).first()
        if category:
            category.url_count = len(category.urls)
        return category

    def get_category_by_name(self, name: str) -> Optional[Category]:
        """Get a category by name.

        Args:
            name: Name of the category

        Returns:
            Category object or None if not found
        """
        return self.db.query(Category).filter(Category.name == name).first()

    def create_category(self, name: str, description: Optional[str] = None) -> Category:
        """Create a new category.

        Args:
            name: Name of the category
            description: Optional description of the category

        Returns:
            Created Category object
        """
        db_category = Category(
            name=name,
            description=description
        )
        self.db.add(db_category)
        self.db.commit()
        self.db.refresh(db_category)
        db_category.url_count = 0  # New category has no URLs
        logger.info(f"Created category: {name}")
        return db_category

    def update_category(self, category_id: int, name: Optional[str] = None, description: Optional[str] = None) -> Optional[Category]:
        """Update a category.

        Args:
            category_id: ID of the category
            name: New name for the category
            description: New description for the category

        Returns:
            Updated Category object or None if not found
        """
        db_category = self.get_category_by_id(category_id)
        if not db_category:
            return None

        if name is not None:
            db_category.name = name
        if description is not None:
            db_category.description = description

        self.db.commit()
        self.db.refresh(db_category)
        db_category.url_count = len(db_category.urls)
        logger.info(f"Updated category ID: {category_id}")
        return db_category

    def delete_category(self, category_id: int) -> bool:
        """Delete a category.

        Args:
            category_id: ID of the category

        Returns:
            True if deleted, False if not found
        """
        db_category = self.get_category_by_id(category_id)
        if not db_category:
            return False

        self.db.delete(db_category)
        self.db.commit()
        logger.info(f"Deleted category ID: {category_id}")
        return True

    def get_urls_by_category(self, category_id: int, active_only: bool = True) -> List[URL]:
        """Get all URLs for a specific category.

        Args:
            category_id: ID of the category
            active_only: Only return active URLs

        Returns:
            List of URL objects
        """
        category = self.get_category_by_id(category_id)
        if not category:
            return []

        urls = category.urls
        if active_only:
            urls = [url for url in urls if url.is_active]

        return urls
