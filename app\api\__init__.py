from fastapi import APIRouter

from app.api import urls, processing, results, settings, categories, scheduler

# Create main API router
api_router = APIRouter()

# Include all routers
api_router.include_router(urls.router)
api_router.include_router(processing.router)
api_router.include_router(results.router)
api_router.include_router(settings.router)
api_router.include_router(categories.router)
api_router.include_router(scheduler.router)
