import sqlite3
import os
import logging
from datetime import datetime
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_db_file():
    # Prefer DATABASE_URL env (used by SQLAlchemy), else DB_FILE env, else default
    db_url = os.environ.get("DATABASE_URL")
    if db_url and db_url.startswith("sqlite:///"):
        # Handles both sqlite:///absolute/path and sqlite:///relative/path
        path = db_url.replace("sqlite:///", "", 1)
        # On Windows, absolute paths may start with a slash (sqlite:////C:/...)
        if path.startswith("/"):
            path = path[1:]
        return path
    return os.environ.get("DB_FILE", "data/database.sqlite")

DB_FILE = get_db_file()

# Default categories
DEFAULT_CATEGORIES = [
    {"name": "Daily", "description": "URLs to check daily"},
    {"name": "Monthly", "description": "URLs to check monthly"},
    {"name": "News", "description": "News sources"},
    {"name": "Tech", "description": "Technology sources"},
    {"name": "Health", "description": "Health and wellness sources"}
]

def add_categories():
    """Add categories table and default categories."""
    if not os.path.exists(DB_FILE):
        logger.error(f"Database file {DB_FILE} not found")
        return False

    try:
        # Connect to the database
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()

        # Check if categories table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='categories'")
        if cursor.fetchone() is None:
            logger.info("Creating categories table...")
            
            # Create categories table
            cursor.execute("""
            CREATE TABLE categories (
                id INTEGER PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                description VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # Create URL-category association table
            cursor.execute("""
            CREATE TABLE url_category (
                url_id INTEGER NOT NULL,
                category_id INTEGER NOT NULL,
                PRIMARY KEY (url_id, category_id),
                FOREIGN KEY (url_id) REFERENCES urls (id) ON DELETE CASCADE,
                FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE
            )
            """)
            
            # Add default categories
            now = datetime.now().isoformat()
            for category in DEFAULT_CATEGORIES:
                cursor.execute(
                    "INSERT INTO categories (name, description, created_at) VALUES (?, ?, ?)",
                    (category["name"], category["description"], now)
                )
                logger.info(f"Added category: {category['name']}")
            
            conn.commit()
            logger.info("Categories table created and populated with default categories")
        else:
            logger.info("Categories table already exists")
            
            # Check if we need to add any default categories
            for category in DEFAULT_CATEGORIES:
                cursor.execute("SELECT id FROM categories WHERE name = ?", (category["name"],))
                if cursor.fetchone() is None:
                    cursor.execute(
                        "INSERT INTO categories (name, description, created_at) VALUES (?, ?, ?)",
                        (category["name"], category["description"], datetime.now().isoformat())
                    )
                    logger.info(f"Added missing default category: {category['name']}")
            
            conn.commit()
            
        conn.close()
        return True
    except Exception as e:
        logger.exception(f"Error adding categories: {str(e)}")
        return False

if __name__ == "__main__":
    add_categories()
