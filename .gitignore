# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Project specific
# Database files
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Sensitive data
.env
.env.local
.env.development
.env.test
.env.production
*.pem
*.key
*.crt

# API keys and secrets
api_keys.py
secrets.py
credentials.json
client_secrets.json
google_credentials.json
gemini_api_key.txt

# Exported data
exports/
*.csv
*.json
*.xlsx
*.xls

# Generated content
generated/
output/
tts_output/
audio/

# Embeddings and vector data
embeddings/
vectors/
*.bin
*.vec
*.npy
*.npz
*.pkl
*.pickle

# Temporary files
tmp/
temp/
.temp/
.tmp/

# Cache
.cache/
__pycache__/
.pytest_cache/

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mypy
.mypy_cache/

# pytest
.pytest_cache/

# Translations
*.mo
*.pot

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Specific to this project
# Exclude any data files that might contain sensitive information
data/
*.db
app/static/audio/
app/static/exports/
tts_result_2.mp3
