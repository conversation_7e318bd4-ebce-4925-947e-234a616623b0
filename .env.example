# Application Settings
APP_NAME="News and More"
DEBUG=True
LOG_LEVEL=INFO

# Database Settings
DATABASE_URL=sqlite:///data/database.sqlite

# LLM Settings
# Ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3

# Google Gemini
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro

# Chunking Settings
CHUNK_SIZE=1000
CHUNK_OVERLAP=100

# Google Sheets API
GOOGLE_SHEETS_CREDENTIALS_FILE=credentials.json
GOOGLE_SHEETS_TOKEN_FILE=token.json
GOOGLE_SHEETS_SPREADSHEET_ID=your_spreadsheet_id_here

# Scheduler Settings
SCHEDULER_ENABLED=True
SCHEDULER_HOUR=3
SCHEDULER_MINUTE=0