import csv
import json
import logging
import os
from datetime import datetime
from enum import Enum
from typing import List, Dict, Any, Optional, Union

import google.auth
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from app.config import settings
from app.database import Result

# Set up logger
logger = logging.getLogger(__name__)

# Google Sheets API scopes
SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]


class ExportFormat(str, Enum):
    """Enum for export formats."""
    JSON = "json"
    CSV = "csv"
    TEXT = "txt"
    MARKDOWN = "md"


class ExportService:
    """Service for exporting results to various formats."""

    def __init__(self):
        """Initialize the export service."""
        self.export_dir = settings.exports_dir
        os.makedirs(self.export_dir, exist_ok=True)

    def export_to_file(self, results: List[Result], format: ExportFormat, filename: Optional[str] = None) -> str:
        """Export results to a file.

        Args:
            results: List of Result objects to export
            format: Export format
            filename: Optional filename (without extension)

        Returns:
            Path to the exported file
        """
        if not results:
            logger.warning("No results to export")
            return ""

        # Generate default filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"export_{timestamp}"

        # Add extension if not present
        extension = format.value  # Get the actual value (e.g., "md" instead of ExportFormat.MARKDOWN)
        if not filename.endswith(f".{extension}"):
            filename = f"{filename}.{extension}"

        # Full path
        filepath = os.path.join(self.export_dir, filename)

        try:
            if format == ExportFormat.JSON:
                self._export_json(results, filepath)
            elif format == ExportFormat.CSV:
                self._export_csv(results, filepath)
            elif format == ExportFormat.TEXT:
                self._export_text(results, filepath)
            elif format == ExportFormat.MARKDOWN:
                self._export_markdown(results, filepath)
            else:
                logger.error(f"Unsupported export format: {format}")
                return ""

            logger.info(f"Exported {len(results)} results to {filepath}")
            return filepath
        except Exception as e:
            logger.exception(f"Error exporting results: {str(e)}")
            return ""

    def _export_json(self, results: List[Result], filepath: str):
        """Export results to JSON format.

        Args:
            results: List of Result objects to export
            filepath: Path to the output file
        """
        data = [self._result_to_dict(result) for result in results]
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

    def _export_csv(self, results: List[Result], filepath: str):
        """Export results to CSV format.

        Args:
            results: List of Result objects to export
            filepath: Path to the output file
        """
        if not results:
            return

        # Convert results to dictionaries
        data = [self._result_to_dict(result) for result in results]

        # Write to CSV
        with open(filepath, "w", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=data[0].keys())
            writer.writeheader()
            writer.writerows(data)

    def _export_text(self, results: List[Result], filepath: str):
        """Export results to plain text format.

        Args:
            results: List of Result objects to export
            filepath: Path to the output file
        """
        with open(filepath, "w", encoding="utf-8") as f:
            for result in results:
                f.write(f"Title: {result.title or 'No title'}\n")
                f.write(f"Source: {result.source_url or 'No source'}\n")
                f.write(f"Processed: {result.processed_at}\n")
                f.write(f"LLM: {result.llm_type} ({result.llm_model})\n\n")
                f.write(f"{result.llm_analysis or 'No analysis'}\n\n")
                f.write("-" * 80 + "\n\n")

    def _export_markdown(self, results: List[Result], filepath: str):
        """Export results to Markdown format.

        Args:
            results: List of Result objects to export
            filepath: Path to the output file
        """
        with open(filepath, "w", encoding="utf-8") as f:
            f.write("# Exported Results\n\n")

            for result in results:
                f.write(f"## {result.title or 'No title'}\n\n")
                f.write(f"**Source:** [{result.source_url or 'No source'}]({result.source_url or '#'})\n\n")
                f.write(f"**Processed:** {result.processed_at}\n\n")
                f.write(f"**LLM:** {result.llm_type} ({result.llm_model})\n\n")
                f.write(f"{result.llm_analysis or 'No analysis'}\n\n")
                f.write("---\n\n")

    def _result_to_dict(self, result: Result) -> Dict[str, Any]:
        """Convert a Result object to a dictionary.

        Args:
            result: Result object to convert

        Returns:
            Dictionary representation of the Result
        """
        return {
            "id": result.id,
            "url_id": result.url_id,
            "title": result.title,
            "source_url": result.source_url,
            "llm_analysis": result.llm_analysis,
            "llm_type": result.llm_type,
            "llm_model": result.llm_model,
            "processed_at": result.processed_at.isoformat() if result.processed_at else None,
            "has_embedding": result.has_embedding
        }

    def export_to_google_sheets(self, results: List[Result], spreadsheet_id: Optional[str] = None) -> bool:
        """Export results to Google Sheets.

        Args:
            results: List of Result objects to export
            spreadsheet_id: Optional Google Sheets spreadsheet ID

        Returns:
            True if successful, False otherwise
        """
        if not results:
            logger.warning("No results to export to Google Sheets")
            return False

        # Use configured spreadsheet ID if not provided
        spreadsheet_id = spreadsheet_id or settings.google_sheets_spreadsheet_id
        if not spreadsheet_id:
            logger.error("Google Sheets spreadsheet ID not provided")
            return False

        # Get credentials
        credentials = self._get_google_credentials()
        if not credentials:
            return False

        try:
            # Build the Sheets API service
            service = build("sheets", "v4", credentials=credentials)

            # Convert results to rows
            header = ["ID", "Title", "Source URL", "LLM Analysis", "LLM Type", "LLM Model", "Processed At"]
            rows = [header]
            for result in results:
                rows.append([
                    result.id,
                    result.title or "No title",
                    result.source_url or "No source",
                    result.llm_analysis or "No analysis",
                    result.llm_type,
                    result.llm_model,
                    result.processed_at.isoformat() if result.processed_at else "N/A"
                ])

            # Create a new sheet with timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            sheet_name = f"Export {timestamp}"

            # Add a new sheet
            sheet_metadata = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
            sheets = sheet_metadata.get("sheets", "")
            sheet_ids = [sheet.get("properties", {}).get("sheetId", 0) for sheet in sheets]
            new_sheet_id = max(sheet_ids) + 1 if sheet_ids else 0

            batch_update_request = {
                "requests": [{
                    "addSheet": {
                        "properties": {
                            "title": sheet_name,
                            "sheetId": new_sheet_id
                        }
                    }
                }]
            }

            service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body=batch_update_request
            ).execute()

            # Write data to the new sheet
            range_name = f"{sheet_name}!A1"
            body = {
                "values": rows
            }

            service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption="RAW",
                body=body
            ).execute()

            logger.info(f"Exported {len(results)} results to Google Sheets")
            return True
        except HttpError as e:
            logger.exception(f"Error exporting to Google Sheets: {str(e)}")
            return False
        except Exception as e:
            logger.exception(f"Unexpected error exporting to Google Sheets: {str(e)}")
            return False

    def _get_google_credentials(self) -> Optional[Credentials]:
        """Get Google API credentials.

        Returns:
            Google API credentials or None if not available
        """
        credentials_file = settings.google_sheets_credentials_file
        token_file = settings.google_sheets_token_file

        if not credentials_file or not os.path.exists(credentials_file):
            logger.error(f"Google credentials file not found: {credentials_file}")
            return None

        try:
            # Check if token file exists
            if token_file and os.path.exists(token_file):
                credentials = Credentials.from_authorized_user_file(token_file, SCOPES)
                if credentials and not credentials.expired:
                    return credentials

            # If no valid token, run the OAuth flow
            flow = InstalledAppFlow.from_client_secrets_file(credentials_file, SCOPES)
            credentials = flow.run_local_server(port=0)

            # Save the credentials for the next run
            if token_file:
                with open(token_file, "w") as token:
                    token.write(credentials.to_json())

            return credentials
        except Exception as e:
            logger.exception(f"Error getting Google credentials: {str(e)}")
            return None
