from typing import List

from langchain.text_splitter import MarkdownTextSplitter

from app.config import settings


class TextChunker:
    """Service for chunking text content."""

    def __init__(self, chunk_size: int = None, chunk_overlap: int = None):
        """Initialize the text chunker.

        Args:
            chunk_size: Maximum size of chunks in tokens
            chunk_overlap: Overlap between chunks in tokens
        """
        self.chunk_size = chunk_size or settings.chunk_size
        self.chunk_overlap = chunk_overlap or settings.chunk_overlap
        self.text_splitter = MarkdownTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )

    def chunk_text(self, text: str) -> List[str]:
        """Split text into chunks.

        Args:
            text: Text to split into chunks

        Returns:
            List of text chunks
        """
        if not text:
            return []

        return self.text_splitter.split_text(text)
