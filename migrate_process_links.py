import sqlite3
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database file path
DB_FILE = "data/database.sqlite"

def migrate_database():
    """Add process_links column to the urls table."""
    if not os.path.exists(DB_FILE):
        logger.error(f"Database file {DB_FILE} not found")
        return False
    
    try:
        # Connect to the database
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check if column already exists
        cursor.execute("PRAGMA table_info(urls)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add process_links column if it doesn't exist
        if "process_links" not in columns:
            logger.info("Adding process_links column to urls table")
            cursor.execute("ALTER TABLE urls ADD COLUMN process_links BOOLEAN DEFAULT 0")
            logger.info("Column added successfully")
        else:
            logger.info("process_links column already exists")
        
        # Commit changes and close connection
        conn.commit()
        conn.close()
        
        logger.info("Database migration completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error migrating database: {str(e)}")
        return False

if __name__ == "__main__":
    migrate_database()
