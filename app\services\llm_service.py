import logging
from typing import List, Optional, Dict, Any, Literal

import google.generativeai as genai
from langchain.llms import Ollama

from app.config import settings
from app.services.chunking import TextChunker

# Set up logger
logger = logging.getLogger(__name__)

# Default prompt templates
DEFAULT_PROMPT_TEMPLATE = """
请分析以下内容，并生成一份**详尽且全面的摘要**。

这份摘要需要在**详细程度**和**简洁性**之间取得良好平衡。它的目标是：
1.  **足够详细**：能够准确、透彻地传达原文的核心信息、主要论点、关键支撑细节和结论。
2.  **独立理解**：让读者**无需回顾原文**就能对内容有扎实和全面的理解。
3.  **保持摘要性质**：避免变成对原文的简单复述或冗长的解释，抓住精髓。
4.  **避免过于简略**：确保包含所有理解原文主旨所必需的关键信息点。

请确保你的摘要涵盖以下方面：
* 清晰地阐述内容的主题、核心议题或目的。
* 详细梳理主要论点、观点或内容的各个关键部分，并包含必要的支撑信息、证据或解释。
* 提及原文中呈现的任何重要发现、数据或具有代表性的示例。
* 总结作者得出的主要结论或讨论的潜在影响/意义。
* 如果原文中有特别精辟或高度概括核心思想的引言（名言警句），酌情引用1-2句最关键的。

请将摘要组织得逻辑清晰、易于理解。
**语言要求**：如果原始内容是中文，请**完全使用中文**提供摘要。不要将其翻译成英文。

原始内容 (CONTENT):
{content}

详尽摘要 (COMPREHENSIVE SUMMARY):
"""

# Define two different aggregation templates - one that preserves all original content and one that's more concise
FULL_AGGREGATION_PROMPT_TEMPLATE = """
You are an expert analyst tasked with reviewing and synthesizing multiple source summaries into a comprehensive report.

Below are summaries from {source_count} different sources that have already been analyzed individually. Your task is to create a detailed report that fulfills the following requirements in two language versions:

**Part 1: Original Language Output**

1.  **Present Original Summaries:**
    * Reproduce the **full, unaltered content** of all the provided source summaries below.
    * You may reorder the summaries for better logical flow if necessary, but **you must not shorten, summarize further, or delete any part of the original text**. Clearly label this section (e.g., "Original Source Summaries").

2.  **Provide Comprehensive Analysis:**
    * **Following** the presentation of the original summaries, provide a unified synthesis and analysis based *on* them.
    * This analysis should:
        * Identify the most important themes, trends, and insights emerging across **all** provided summaries.
        * Highlight specific areas of consensus and disagreement between the sources, referencing points from the summaries.
        * Offer a balanced perspective that acknowledges the different viewpoints or findings presented.
        * Explicitly note the most valuable or unique information contributed by each source summary.
        * Organize the analysis section in a clear, logical structure (e.g., using headings for themes, consensus/disagreement, etc.).
        * Draw meaningful conclusions and discuss potential implications based on the collective information from the summaries.
    * Clearly label this section (e.g., "Comprehensive Analysis").

3.  **Language:**
    * If the original source summaries (`{sources}`) are in a language other than English, provide this entire Part 1 (Original Summaries + Comprehensive Analysis) in **that same language**.
    * If the sources are in English, provide Part 1 in English.

**Part 2: Simplified Chinese Translation**

1.  **Translate Entire Output if part1 is not Simplified Chinese (简体中文):**
    * Provide a **complete and accurate translation** of the *entire* Part 1 (including the reproduced Original Summaries and your Comprehensive Analysis) into **Simplified Chinese (简体中文)**.
    * Maintain the structure established in Part 1 (e.g., keep the summaries separate from the analysis).
    * Clearly label this section (e.g., "简体中文翻译 / Simplified Chinese Translation").

**Input Sources:**

SOURCES:
{sources}

**Output Structure:**

Please structure your final response clearly, first presenting Part 1 (Original Language: Original Summaries + Comprehensive Analysis) and then Part 2 (Simplified Chinese Translation of Part 1).
"""

CONCISE_AGGREGATION_PROMPT_TEMPLATE = """
You are an expert analyst tasked with synthesizing multiple source summaries into a comprehensive report.

Below are summaries from {source_count} different sources that have already been analyzed individually. Your task is to create a detailed synthesis that identifies key themes, insights, and connections across all sources.

**Part 1: Comprehensive Analysis in Original Language**

Create a unified analysis that:
1. Identifies the most important themes, trends, and insights across all sources
2. Highlights areas of consensus and disagreement between sources
3. Provides a balanced perspective that considers all viewpoints
4. Notes the most valuable information from each source
5. Organizes the information in a clear, logical structure
6. Offers meaningful conclusions based on the collective information

If the content is in a language other than English, provide your analysis in that same language.

**Part 2: Simplified Chinese Translation**

Provide a complete translation of your analysis into Simplified Chinese (简体中文).

SOURCES:
{sources}

COMPREHENSIVE ANALYSIS:
"""

# Define a more concise aggregation template that focuses on analysis without reproducing all original content
AGGREGATION_PROMPT_TEMPLATE = """
You are an expert analyst tasked with synthesizing multiple source summaries into a comprehensive report.

Below are summaries from {source_count} different sources that have already been analyzed individually. Your task is to create a detailed synthesis that identifies key themes, insights, and connections across all sources.

**Part 1: Comprehensive Analysis in Original Language**

Create a unified analysis that:
1. Identifies the most important themes, trends, and insights across all sources
2. Highlights areas of consensus and disagreement between sources
3. Provides a balanced perspective that considers all viewpoints
4. Notes the most valuable information from each source
5. Organizes the information in a clear, logical structure with headings
6. Offers meaningful conclusions based on the collective information

If the content is in a language other than English, provide your analysis in that same language.

**Part 2: Simplified Chinese Translation**

Provide a complete translation of your analysis into Simplified Chinese (简体中文).

SOURCES:
{sources}

COMPREHENSIVE ANALYSIS:
"""


class LLMService:
    """Service for interacting with LLMs."""

    def __init__(self, llm_type: Optional[Literal["ollama", "gemini"]] = None):
        """Initialize the LLM service.

        Args:
            llm_type: Type of LLM to use ("ollama" or "gemini")
        """
        self.llm_type = llm_type or settings.default_llm
        self.chunker = TextChunker()

        # Initialize the selected LLM
        if self.llm_type == "ollama":
            self._init_ollama()
        elif self.llm_type == "gemini":
            self._init_gemini()
        else:
            raise ValueError(f"Unsupported LLM type: {self.llm_type}")

    def _init_ollama(self):
        """Initialize Ollama LLM."""
        try:
            self.llm = Ollama(
                base_url=settings.ollama_base_url,
                model=settings.ollama_model
            )
            self.model_name = settings.ollama_model
            logger.info(f"Initialized Ollama LLM with model {settings.ollama_model}")
        except Exception as e:
            logger.exception(f"Failed to initialize Ollama LLM: {str(e)}")
            raise

    def _init_gemini(self):
        """Initialize Google Gemini LLM."""
        if not settings.gemini_api_key:
            raise ValueError("Gemini API key is not set")

        try:
            genai.configure(api_key=settings.gemini_api_key)
            self.model_name = settings.gemini_model
            self.generation_config = {
                "temperature": 0.2,
                "top_p": 0.95,
                "top_k": 40,
                "max_output_tokens": 30000,  # Increased to handle large aggregated results
            }
            logger.info(f"Initialized Google Gemini LLM with model {settings.gemini_model}")
        except Exception as e:
            logger.exception(f"Failed to initialize Google Gemini LLM: {str(e)}")
            raise

    async def analyze_content(self, content: str, prompt_template: Optional[str] = None) -> Dict[str, Any]:
        """Analyze content using the selected LLM.

        Args:
            content: Content to analyze
            prompt_template: Custom prompt template to use

        Returns:
            Dictionary containing analysis result and metadata
        """
        if not content:
            return {
                "analysis": "",
                "llm_type": self.llm_type,
                "model": self.model_name,
                "error": "No content provided"
            }

        # Use default prompt template if none provided
        prompt_template = prompt_template or DEFAULT_PROMPT_TEMPLATE

        # For Gemini models, we can skip chunking due to the large context window (1M tokens)
        if self.llm_type == "gemini" and len(content) < 800000:  # Conservative limit for Gemini
            chunks = [content]  # Use the entire content as a single chunk
            logger.info("Using Gemini with full content (no chunking)")
        else:
            # Chunk the content for other models or very large content
            chunks = self.chunker.chunk_text(content)
            logger.info(f"Split content into {len(chunks)} chunks")

        # Process each chunk and aggregate results
        try:
            if self.llm_type == "ollama":
                result = await self._process_with_ollama(chunks, prompt_template)
            else:  # gemini
                result = await self._process_with_gemini(chunks, prompt_template)

            return {
                "analysis": result,
                "llm_type": self.llm_type,
                "model": self.model_name,
                "error": None
            }
        except Exception as e:
            logger.exception(f"Error analyzing content with {self.llm_type}: {str(e)}")
            return {
                "analysis": "",
                "llm_type": self.llm_type,
                "model": self.model_name,
                "error": str(e)
            }

    async def aggregate_analyses(self, analyses: List[Dict[str, Any]], prompt_template: Optional[str] = None) -> Dict[str, Any]:
        """Aggregate multiple analyses into a comprehensive report.

        Args:
            analyses: List of analysis dictionaries, each containing at least 'title' and 'llm_analysis'
            prompt_template: Custom prompt template to use

        Returns:
            Dictionary containing aggregated analysis result and metadata
        """
        if not analyses:
            return {
                "analysis": "",
                "llm_type": self.llm_type,
                "model": self.model_name,
                "error": "No analyses provided"
            }

        # Use default aggregation prompt template if none provided
        prompt_template = prompt_template or AGGREGATION_PROMPT_TEMPLATE

        # Prepare the sources text
        sources_text = ""
        for i, analysis in enumerate(analyses):
            title = analysis.get("title", f"Source {i+1}")
            content = analysis.get("llm_analysis", "")
            if not content:
                continue

            sources_text += f"SOURCE {i+1}: {title}\n\n{content}\n\n"

        if not sources_text:
            return {
                "analysis": "",
                "llm_type": self.llm_type,
                "model": self.model_name,
                "error": "No valid content in analyses"
            }

        # Format the prompt with the sources and count
        formatted_prompt = prompt_template.format(
            source_count=len(analyses),
            sources=sources_text
        )

        # Process with the appropriate LLM
        try:
            if self.llm_type == "ollama":
                # Use Ollama directly without chunking for aggregation
                result = self.llm.invoke(formatted_prompt)
            else:  # gemini
                # Use Gemini directly without chunking for aggregation
                model = genai.GenerativeModel(
                    model_name=self.model_name,
                    generation_config=self.generation_config
                )
                response = model.generate_content(formatted_prompt)
                result = response.text

            return {
                "analysis": result,
                "llm_type": self.llm_type,
                "model": self.model_name,
                "source_count": len(analyses),
                "error": None
            }
        except Exception as e:
            logger.exception(f"Error aggregating analyses with {self.llm_type}: {str(e)}")
            return {
                "analysis": "",
                "llm_type": self.llm_type,
                "model": self.model_name,
                "source_count": len(analyses),
                "error": str(e)
            }

    async def _process_with_ollama(self, chunks: List[str], prompt_template: str) -> str:
        """Process content chunks with Ollama.

        Args:
            chunks: List of content chunks
            prompt_template: Prompt template to use

        Returns:
            Aggregated analysis result
        """
        results = []

        for i, chunk in enumerate(chunks):
            try:
                prompt = prompt_template.format(content=chunk)
                response = self.llm.invoke(prompt)
                results.append(response)
                logger.info(f"Processed chunk {i+1}/{len(chunks)} with Ollama")
            except Exception as e:
                logger.error(f"Error processing chunk {i+1} with Ollama: {str(e)}")
                results.append(f"[Error processing chunk {i+1}: {str(e)}]")

        # Combine results
        if len(chunks) > 1:
            combined_results = "\n\n".join(results)
            # Generate a final summary if there are multiple chunks
            final_prompt = f"""Synthesize the following summaries into a coherent, comprehensive analysis:

{combined_results}

FINAL ANALYSIS:"""
            try:
                final_result = self.llm.invoke(final_prompt)
                return final_result
            except Exception as e:
                logger.error(f"Error generating final summary with Ollama: {str(e)}")
                return combined_results
        elif results:
            return results[0]
        else:
            return ""

    async def _process_with_gemini(self, chunks: List[str], prompt_template: str) -> str:
        """Process content chunks with Google Gemini.

        Args:
            chunks: List of content chunks
            prompt_template: Prompt template to use

        Returns:
            Aggregated analysis result
        """
        results = []

        model = genai.GenerativeModel(
            model_name=self.model_name,
            generation_config=self.generation_config
        )

        for i, chunk in enumerate(chunks):
            try:
                prompt = prompt_template.format(content=chunk)
                response = model.generate_content(prompt)
                results.append(response.text)
                logger.info(f"Processed chunk {i+1}/{len(chunks)} with Gemini")
            except Exception as e:
                logger.error(f"Error processing chunk {i+1} with Gemini: {str(e)}")
                results.append(f"[Error processing chunk {i+1}: {str(e)}]")

        # Combine results
        if len(chunks) > 1:
            combined_results = "\n\n".join(results)
            # Generate a final summary if there are multiple chunks
            final_prompt = f"""Synthesize the following summaries into a coherent, comprehensive analysis:

{combined_results}

FINAL ANALYSIS:"""
            try:
                final_response = model.generate_content(final_prompt)
                return final_response.text
            except Exception as e:
                logger.error(f"Error generating final summary with Gemini: {str(e)}")
                return combined_results
        elif results:
            return results[0]
        else:
            return ""
