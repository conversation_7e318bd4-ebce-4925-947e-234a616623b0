import json
from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.database import get_db, AggregatedResult
from app.services.storage import StorageService
from app.services.embedding import EmbeddingService
from app.services.export import ExportService, ExportFormat
from app.services.tts_service import TTSService, TTSEngine

# Create router
router = APIRouter(prefix="/api/results", tags=["Results"])


# Models
class ResultResponse(BaseModel):
    """Model for result response."""
    id: int
    url_id: int
    title: Optional[str] = None
    source_url: Optional[str] = None
    llm_analysis: Optional[str] = None
    llm_type: str
    llm_model: str
    processed_at: Optional[datetime] = None
    has_embedding: bool

    class Config:
        orm_mode = True
        from_attributes = True


class SearchRequest(BaseModel):
    """Model for search request."""
    query: str
    limit: int = 5


class SearchResult(BaseModel):
    """Model for search result."""
    result_id: int
    title: str
    source: str
    distance: float


class SearchResponse(BaseModel):
    """Model for search response."""
    query: str
    results: List[SearchResult]


class ExportRequest(BaseModel):
    """Model for export request."""
    result_ids: List[int]
    format: str  # "json", "csv", "txt", "md"
    filename: Optional[str] = None


class ExportResponse(BaseModel):
    """Model for export response."""
    success: bool
    filepath: str
    message: str


class TTSRequest(BaseModel):
    """Model for TTS request."""
    result_id: int
    engine: str = "pyttsx3"  # "pyttsx3" or "gtts"


class TTSResponse(BaseModel):
    """Model for TTS response."""
    success: bool
    filepath: str
    message: str


class AggregatedResultResponse(BaseModel):
    """Model for aggregated result response."""
    id: int
    title: str
    source_count: int
    aggregated_analysis: str
    llm_type: str
    llm_model: str
    processed_at: datetime
    result_ids: List[int]

    class Config:
        orm_mode = True
        from_attributes = True


# Routes
@router.get("", response_model=List[ResultResponse])
async def get_results(url_id: Optional[int] = None, db: Session = Depends(get_db)):
    """Get all results, optionally filtered by URL ID."""
    storage_service = StorageService(db)
    results = storage_service.get_results(url_id=url_id)
    return results


@router.get("/{result_id}", response_model=ResultResponse)
async def get_result(result_id: int, db: Session = Depends(get_db)):
    """Get a result by ID."""
    storage_service = StorageService(db)
    result = storage_service.get_result_by_id(result_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Result with ID {result_id} not found"
        )
    return result


@router.delete("/{result_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_result(result_id: int, db: Session = Depends(get_db)):
    """Delete a result."""
    storage_service = StorageService(db)

    # Check if result exists
    result = storage_service.get_result_by_id(result_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Result with ID {result_id} not found"
        )

    # Remove from embedding index if it has an embedding
    if result.has_embedding:
        embedding_service = EmbeddingService()
        embedding_service.remove_from_index(result_id)

    # Delete result
    storage_service.delete_result(result_id)
    return None


@router.post("/search", response_model=SearchResponse)
async def search_results(request: SearchRequest):
    """Search for results using vector embeddings."""
    embedding_service = EmbeddingService()
    search_results = embedding_service.search(request.query, k=request.limit)

    return SearchResponse(
        query=request.query,
        results=[SearchResult(
            result_id=result["result_id"],
            title=result["title"] or "No title",
            source=result["source"] or "No source",
            distance=result["distance"]
        ) for result in search_results]
    )


@router.post("/export", response_model=ExportResponse)
async def export_results(request: ExportRequest, db: Session = Depends(get_db)):
    """Export results to a file."""
    storage_service = StorageService(db)
    export_service = ExportService()

    # Get results
    results = []
    for result_id in request.result_ids:
        result = storage_service.get_result_by_id(result_id)
        if result:
            results.append(result)

    if not results:
        return ExportResponse(
            success=False,
            filepath="",
            message="No results found to export"
        )

    # Validate format
    try:
        export_format = ExportFormat(request.format)
    except ValueError:
        return ExportResponse(
            success=False,
            filepath="",
            message=f"Invalid export format: {request.format}"
        )

    # Export results
    filepath = export_service.export_to_file(results, export_format, request.filename)

    if not filepath:
        return ExportResponse(
            success=False,
            filepath="",
            message="Failed to export results"
        )

    return ExportResponse(
        success=True,
        filepath=filepath,
        message=f"Exported {len(results)} results to {filepath}"
    )


@router.post("/tts", response_model=TTSResponse)
async def text_to_speech(request: TTSRequest, db: Session = Depends(get_db)):
    """Convert result analysis to speech."""
    storage_service = StorageService(db)

    # Get result
    result = storage_service.get_result_by_id(request.result_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Result with ID {request.result_id} not found"
        )

    if not result.llm_analysis:
        return TTSResponse(
            success=False,
            filepath="",
            message="No analysis text available for TTS"
        )

    # Validate engine
    try:
        tts_engine = TTSEngine(request.engine)
    except ValueError:
        return TTSResponse(
            success=False,
            filepath="",
            message=f"Invalid TTS engine: {request.engine}"
        )

    # Generate TTS
    tts_service = TTSService(engine=tts_engine)
    filename = f"tts_result_{result.id}"
    filepath = tts_service.text_to_speech(result.llm_analysis, filename)

    if not filepath:
        return TTSResponse(
            success=False,
            filepath="",
            message="Failed to generate TTS audio"
        )

    return TTSResponse(
        success=True,
        filepath=filepath,
        message=f"Generated TTS audio: {filepath}"
    )


@router.post("/{result_id}/embedding", response_model=ResultResponse)
async def generate_embedding(result_id: int, db: Session = Depends(get_db)):
    """Generate embedding for a result."""
    storage_service = StorageService(db)

    # Get result
    result = storage_service.get_result_by_id(result_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Result with ID {result_id} not found"
        )

    if not result.llm_analysis:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No analysis text available for embedding"
        )

    # Generate embedding
    embedding_service = EmbeddingService()
    success = embedding_service.add_to_index(
        result_id=result.id,
        text=result.llm_analysis,
        title=result.title or "",
        source=result.source_url or ""
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate embedding"
        )

    # Update embedding status
    result = storage_service.update_embedding_status(result.id, True)
    return result


# Aggregated Results Routes
@router.get("/aggregated/", response_model=List[AggregatedResultResponse])
async def get_aggregated_results(db: Session = Depends(get_db)):
    """Get all aggregated results."""
    storage_service = StorageService(db)
    results = storage_service.get_aggregated_results()

    # Convert result_ids from JSON string to list for each result
    for result in results:
        if isinstance(result.result_ids, str):
            try:
                result.result_ids = json.loads(result.result_ids)
            except json.JSONDecodeError:
                result.result_ids = []

    return results


@router.get("/aggregated/{aggregated_id}", response_model=AggregatedResultResponse)
async def get_aggregated_result(aggregated_id: int, db: Session = Depends(get_db)):
    """Get an aggregated result by ID."""
    storage_service = StorageService(db)
    result = storage_service.get_aggregated_result_by_id(aggregated_id)

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Aggregated result with ID {aggregated_id} not found"
        )

    # Convert result_ids from JSON string to list
    if isinstance(result.result_ids, str):
        try:
            result.result_ids = json.loads(result.result_ids)
        except json.JSONDecodeError:
            result.result_ids = []

    return result


@router.delete("/aggregated/{aggregated_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_aggregated_result(aggregated_id: int, db: Session = Depends(get_db)):
    """Delete an aggregated result."""
    storage_service = StorageService(db)

    # Check if aggregated result exists
    result = storage_service.get_aggregated_result_by_id(aggregated_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Aggregated result with ID {aggregated_id} not found"
        )

    # Delete aggregated result
    storage_service.delete_aggregated_result(aggregated_id)
    return None


@router.post("/aggregated/tts", response_model=TTSResponse)
async def generate_aggregated_tts(request: TTSRequest, db: Session = Depends(get_db)):
    """Generate text-to-speech for an aggregated result."""
    storage_service = StorageService(db)
    tts_service = TTSService()

    # Get aggregated result
    result = storage_service.get_aggregated_result_by_id(request.result_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Aggregated result with ID {request.result_id} not found"
        )

    # Generate TTS
    try:
        engine = TTSEngine(request.engine)
        tts_service = TTSService(engine=engine)
        filepath = tts_service.text_to_speech(
            text=result.aggregated_analysis,
            filename=f"aggregated_{result.id}"
        )

        return TTSResponse(
            success=True,
            filepath=filepath,
            message="TTS generated successfully"
        )
    except Exception as e:
        return TTSResponse(
            success=False,
            filepath="",
            message=f"Error generating TTS: {str(e)}"
        )
