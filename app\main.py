import logging
import os
from datetime import datetime
from typing import Callable

from fastapi import <PERSON>AP<PERSON>, Request, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session

from app.api import api_router
from app.config import settings
from app.database import init_db, get_db
from app.services.scheduler import scheduler_service
from app.services.storage import StorageService
from app.services.jina_service import JinaService
from app.services.llm_service import LLMService

# Configure logging
log_file = os.path.join(settings.logs_dir, f"app_{datetime.now().strftime('%Y%m%d')}.log")
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title=settings.app_name, debug=settings.debug)

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Set up templates
templates = Jinja2Templates(directory="app/templates")

# Include API routes
app.include_router(api_router)


# Background processing function for scheduler
async def process_all_urls(db: Session):
    """Process all active URLs."""
    logger.info("Starting scheduled processing of all active URLs")
    storage_service = StorageService(db)
    jina_service = JinaService()
    llm_service = LLMService()

    # Get all active URLs
    urls = storage_service.get_urls(active_only=True)

    for url in urls:
        try:
            # Fetch content with tag filtering and process links if specified
            content_data = await jina_service.fetch_content(
                url=url.url,
                include_tags=url.include_tags,
                exclude_tags=url.exclude_tags,
                process_links=url.process_links
            )

            if content_data.get("error"):
                logger.error(f"Error fetching content for URL {url.url}: {content_data.get('error')}")
                continue

            # Analyze content
            markdown_content = content_data.get("markdown_content", "")
            analysis_result = await llm_service.analyze_content(markdown_content)

            if analysis_result.get("error"):
                logger.error(f"Error analyzing content for URL {url.url}: {analysis_result.get('error')}")
                continue

            # Create result
            result_data = {
                "title": content_data.get("title"),
                "source_url": content_data.get("source_url"),
                "markdown_content": markdown_content,
                "llm_analysis": analysis_result.get("analysis"),
                "llm_type": analysis_result.get("llm_type"),
                "llm_model": analysis_result.get("model")
            }

            storage_service.create_result(url.id, result_data)

            # Update URL processed time
            storage_service.update_url_processed_time(url.id)

            logger.info(f"Successfully processed URL: {url.url}")
        except Exception as e:
            logger.exception(f"Error processing URL {url.url}: {str(e)}")

    logger.info(f"Completed scheduled processing of {len(urls)} URLs")


# Dependency to get a database session for the scheduler
def get_scheduler_db():
    """Get a database session for the scheduler."""
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()


# Set DATABASE_URL env so add_categories.py and add_scheduled_jobs.py use the same DB as SQLAlchemy
import os
from app.config import settings
os.environ["DATABASE_URL"] = settings.database_url

# Set DB_FILE environment variable so add_categories.py and add_scheduled_jobs.py use the correct DB path
import os
from app.config import settings
os.environ["DB_FILE"] = str(settings.data_dir / "database.sqlite")

# Import the migration functions
try:
    # Try import from app (for Docker and some setups)
    from app.add_categories import add_categories
except ImportError:
    # Fallback to project root (for local/dev setups)
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from add_categories import add_categories
try:
    from app.add_scheduled_jobs import add_scheduled_jobs_table
except ImportError:
    from add_scheduled_jobs import add_scheduled_jobs_table

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    logger.info("Starting application")

    # Initialize database
    init_db()
    logger.info("Database initialized")

    # Initialize categories
    add_categories()
    logger.info("Categories initialized")

    # Initialize scheduled jobs table
    add_scheduled_jobs_table()
    logger.info("Scheduled jobs table initialized")

    # Start scheduler
    if settings.scheduler_enabled:
        # Set up the database session for the scheduler
        db = next(get_scheduler_db())
        scheduler_service.db = db

        # Start the scheduler
        scheduler_service.start()
        logger.info("Scheduler started")

        # Only add the system job if system_job_enabled is True
        if settings.system_job_enabled:
            # Add system job for processing all URLs
            async def process_wrapper():
                """Wrapper function to pass the database session."""
                await process_all_urls(db)

            # Create a non-async wrapper for the scheduler
            def wrapper():
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(process_wrapper())
                finally:
                    loop.close()

            # Add the system job to the scheduler
            scheduler_service.add_job(
                job_id="process_all_urls",
                func=wrapper,
                hour=settings.scheduler_hour,
                minute=settings.scheduler_minute
            )

            logger.info(f"Scheduled system job to process all URLs at {settings.scheduler_hour:02d}:{settings.scheduler_minute:02d}")
        else:
            logger.info("System job for processing all URLs is disabled")

        # Restart the scheduler to load all jobs from the database
        # This will also add the user-defined jobs from the database
        scheduler_service.restart()
        logger.info("Scheduler restarted with all jobs loaded")


# Shutdown event
@app.on_event("shutdown")
def shutdown_event():
    """Clean up resources on shutdown."""
    logger.info("Shutting down application")

    # Shutdown scheduler
    if scheduler_service.scheduler.running:
        scheduler_service.shutdown()
        logger.info("Scheduler shutdown")


# Routes
@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """Render the index page."""
    return templates.TemplateResponse("index.html", {"request": request, "now": datetime.now(), "settings": settings})


@app.get("/results", response_class=HTMLResponse)
async def results_page(request: Request):
    """Render the results page."""
    return templates.TemplateResponse("results.html", {"request": request, "now": datetime.now(), "settings": settings})


@app.get("/settings", response_class=HTMLResponse)
async def settings_page(request: Request):
    """Render the settings page."""
    return templates.TemplateResponse("settings.html", {"request": request, "now": datetime.now(), "settings": settings})


# Error handlers
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Handle all unhandled exceptions."""
    logger.exception(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


# Run the application
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8181, reload=True)
