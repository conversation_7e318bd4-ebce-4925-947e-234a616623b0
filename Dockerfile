FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libsqlite3-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy app source and config
COPY app ./app
COPY add_categories.py .
COPY add_scheduled_jobs.py .

# Create data directory for persistent storage
RUN mkdir -p /data

# Set environment variable for SQLite DB path
ENV DB_PATH=/data/app.db

# Set environment variable to indicate Docker
ENV IN_DOCKER=1

# Expose the app port
EXPOSE 8181

# Start the app
CMD ["python", "-m", "app.main"]
