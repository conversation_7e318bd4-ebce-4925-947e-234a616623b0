import os
from pathlib import Path
from typing import Optional, Literal

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

# Detect if running in Docker by checking for /.dockerenv or environment variable
IN_DOCKER = os.path.exists("/.dockerenv") or os.getenv("IN_DOCKER") == "1"

# Base directory
BASE_DIR = Path(__file__).resolve().parent.parent

# Always use data/database.sqlite, absolute in Docker, relative locally
if IN_DOCKER:
    DATABASE_URL = "sqlite:////data/database.sqlite"
    DATA_DIR = Path("/data")
else:
    DATABASE_URL = os.getenv("DATABASE_URL", f"sqlite:///{(BASE_DIR / 'data' / 'database.sqlite').as_posix()}")
    DATA_DIR = BASE_DIR / "data"

class Settings(BaseSettings):
    """Application settings."""

    # Application settings
    app_name: str = "News and More"
    debug: bool = False
    log_level: str = "INFO"

    # Database settings
    database_url: str = DATABASE_URL

    # LLM settings
    # Ollama
    ollama_base_url: str = "http://localhost:11434"
    ollama_model: str = "llama3"

    # Google Gemini
    gemini_api_key: Optional[str] = None
    gemini_model: str = "gemini-pro"

    # Default LLM to use
    default_llm: Literal["ollama", "gemini"] = "gemini"

    # Chunking settings
    chunk_size: int = Field(default=1000, ge=100, le=1000000)  # Up to 1M tokens for Gemini
    chunk_overlap: int = Field(default=100, ge=0, le=1000)

    # Google Sheets API
    google_sheets_credentials_file: Optional[str] = None
    google_sheets_token_file: Optional[str] = None
    google_sheets_spreadsheet_id: Optional[str] = None

    # Scheduler settings
    scheduler_enabled: bool = True
    scheduler_hour: int = Field(default=3, ge=0, le=23)
    scheduler_minute: int = Field(default=0, ge=0, le=59)
    system_job_enabled: bool = True  # Controls whether the system job is created

    # Paths
    data_dir: Path = DATA_DIR
    logs_dir: Path = data_dir / "logs"
    exports_dir: Path = data_dir / "exports"
    vector_store_dir: Path = data_dir / "vector_store"

    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", extra="ignore")


# Create settings instance
settings = Settings()

# Ensure directories exist
os.makedirs(settings.logs_dir, exist_ok=True)
os.makedirs(settings.exports_dir, exist_ok=True)
os.makedirs(settings.vector_store_dir, exist_ok=True)
