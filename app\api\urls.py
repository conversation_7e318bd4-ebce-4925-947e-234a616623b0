from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, HttpUrl
from sqlalchemy.orm import Session

from app.database import get_db
from app.services.storage import StorageService
from app.api.categories import CategoryResponse

# Create router
router = APIRouter(prefix="/api/urls", tags=["URLs"])


# Models
class URLBase(BaseModel):
    """Base model for URL data."""
    url: str


class URLCreate(URLBase):
    """Model for creating a URL."""
    include_tags: Optional[str] = None
    exclude_tags: Optional[str] = None
    process_links: bool = False
    category_ids: Optional[List[int]] = None


class URLUpdate(BaseModel):
    """Model for updating a URL."""
    url: Optional[HttpUrl] = None
    is_active: Optional[bool] = None
    include_tags: Optional[str] = None
    exclude_tags: Optional[str] = None
    process_links: Optional[bool] = None
    category_ids: Optional[List[int]] = None


class URLResponse(URLBase):
    """Model for URL response."""
    id: int
    is_active: bool
    last_processed_at: Optional[datetime] = None
    include_tags: Optional[str] = None
    exclude_tags: Optional[str] = None
    process_links: bool = False
    categories: List[CategoryResponse] = []

    model_config = {
        "from_attributes": True
    }


# Routes
@router.get("", response_model=List[URLResponse])
async def get_urls(active_only: bool = False, db: Session = Depends(get_db)):
    """Get all URLs.

    Args:
        active_only: If True, only return active URLs. Default is False to show all URLs.
        db: Database session
    """
    storage_service = StorageService(db)
    urls = storage_service.get_urls(active_only=active_only)
    return urls


@router.get("/{url_id}", response_model=URLResponse)
async def get_url(url_id: int, db: Session = Depends(get_db)):
    """Get a URL by ID."""
    storage_service = StorageService(db)
    url = storage_service.get_url_by_id(url_id)
    if not url:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"URL with ID {url_id} not found"
        )
    return url


@router.post("", response_model=URLResponse, status_code=status.HTTP_201_CREATED)
async def create_url(url_data: URLCreate, db: Session = Depends(get_db)):
    """Create a new URL."""
    storage_service = StorageService(db)

    # Check if URL already exists
    existing_url = storage_service.get_url_by_url(str(url_data.url))
    if existing_url:
        return existing_url

    # Create new URL with tag filters, process_links flag, and categories
    url = storage_service.create_url(
        url=str(url_data.url),
        include_tags=url_data.include_tags,
        exclude_tags=url_data.exclude_tags,
        process_links=url_data.process_links,
        category_ids=url_data.category_ids or []
    )
    return url


@router.put("/{url_id}", response_model=URLResponse)
async def update_url(url_id: int, url_data: URLUpdate, db: Session = Depends(get_db)):
    """Update a URL."""
    storage_service = StorageService(db)

    # Check if URL exists
    existing_url = storage_service.get_url_by_id(url_id)
    if not existing_url:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"URL with ID {url_id} not found"
        )

    # Prepare update data
    update_data = {}
    if url_data.url is not None:
        update_data["url"] = str(url_data.url)
    if url_data.is_active is not None:
        update_data["is_active"] = url_data.is_active
    if url_data.include_tags is not None:
        update_data["include_tags"] = url_data.include_tags
    if url_data.exclude_tags is not None:
        update_data["exclude_tags"] = url_data.exclude_tags
    if url_data.process_links is not None:
        update_data["process_links"] = url_data.process_links
    if url_data.category_ids is not None:
        update_data["category_ids"] = url_data.category_ids

    # Update URL
    updated_url = storage_service.update_url(url_id, update_data)
    return updated_url


@router.delete("/{url_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_url(url_id: int, db: Session = Depends(get_db)):
    """Delete a URL."""
    storage_service = StorageService(db)

    # Check if URL exists
    existing_url = storage_service.get_url_by_id(url_id)
    if not existing_url:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"URL with ID {url_id} not found"
        )

    # Delete URL
    storage_service.delete_url(url_id)
    return None


class TestUrlRequest(BaseModel):
    """Model for test URL request."""
    url: str
    include_tags: Optional[str] = None
    exclude_tags: Optional[str] = None
    process_links: bool = False


class TestUrlResponse(BaseModel):
    """Model for test URL response."""
    url: str
    title: Optional[str] = None
    source_url: Optional[str] = None
    content_length: int
    content_preview: str
    success: bool
    error: Optional[str] = None
    processed_links: Optional[int] = None


@router.post("/test", response_model=TestUrlResponse)
async def test_url(request: TestUrlRequest):
    """Test a URL with Jina without processing with LLM."""
    from app.services.jina_service import JinaService

    jina_service = JinaService()

    try:
        # Fetch content from URL using Jina with tag filtering and process links
        result = await jina_service.fetch_content(
            url=request.url,
            include_tags=request.include_tags,
            exclude_tags=request.exclude_tags,
            process_links=request.process_links
        )

        # Prepare full content without chunking
        content = result.get("markdown_content", "")
        # No truncation for preview - show the full content
        content_preview = content

        return {
            "url": request.url,
            "title": result.get("title"),
            "source_url": result.get("source_url"),
            "content_length": len(content) if content else 0,
            "content_preview": content_preview,
            "success": result.get("error") is None,
            "error": result.get("error"),
            "processed_links": result.get("processed_links")
        }
    except Exception as e:
        return {
            "url": request.url,
            "title": None,
            "source_url": None,
            "content_length": 0,
            "content_preview": "",
            "success": False,
            "error": str(e)
        }
