import sqlite3
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database file path
DB_FILE = "data/database.sqlite"

def migrate_database():
    """Add include_tags and exclude_tags columns to the urls table."""
    if not os.path.exists(DB_FILE):
        logger.error(f"Database file {DB_FILE} not found")
        return False

    try:
        # Connect to the database
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()

        # Check if columns already exist
        cursor.execute("PRAGMA table_info(urls)")
        columns = [column[1] for column in cursor.fetchall()]

        # Add include_tags column if it doesn't exist
        if "include_tags" not in columns:
            logger.info("Adding include_tags column to urls table")
            cursor.execute("ALTER TABLE urls ADD COLUMN include_tags TEXT")
        else:
            logger.info("include_tags column already exists")

        # Add exclude_tags column if it doesn't exist
        if "exclude_tags" not in columns:
            logger.info("Adding exclude_tags column to urls table")
            cursor.execute("ALTER TABLE urls ADD COLUMN exclude_tags TEXT")
        else:
            logger.info("exclude_tags column already exists")

        # Commit changes and close connection
        conn.commit()
        conn.close()

        logger.info("Database migration completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error migrating database: {str(e)}")
        return False

if __name__ == "__main__":
    migrate_database()
