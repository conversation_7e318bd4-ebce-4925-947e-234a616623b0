import json
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.database import get_db
from app.services.jina_service import JinaService
from app.services.llm_service import LLMService
from app.services.storage import StorageService
from app.services.embedding import EmbeddingService

# Create router
router = APIRouter(prefix="/api/processing", tags=["Processing"])


# Models
class ProcessURLRequest(BaseModel):
    """Model for processing a URL."""
    url_id: int
    llm_type: Optional[str] = None  # "ollama" or "gemini"
    generate_embedding: bool = False


class ProcessURLResponse(BaseModel):
    """Model for processing response."""
    url_id: int
    result_id: Optional[int] = None
    status: str
    message: str


class ProcessBatchRequest(BaseModel):
    """Model for batch processing URLs."""
    url_ids: List[int]
    llm_type: Optional[str] = None  # "ollama" or "gemini"
    generate_embedding: bool = False


class ProcessBatchResponse(BaseModel):
    """Model for batch processing response."""
    total: int
    processed: int
    failed: int
    results: List[ProcessURLResponse]


class AggregateRequest(BaseModel):
    """Model for aggregating results."""
    result_ids: List[int]
    llm_type: Optional[str] = None  # "ollama" or "gemini"
    title: Optional[str] = None


class AggregateResponse(BaseModel):
    """Model for aggregation response."""
    id: Optional[int] = None
    title: str
    source_count: int
    aggregated_analysis: str
    llm_type: str
    llm_model: str
    result_ids: List[int]


# Background processing function
async def process_url_background(url_id: int, llm_type: Optional[str], generate_embedding: bool, db: Session):
    """Process a URL in the background.

    Args:
        url_id: ID of the URL to process
        llm_type: Type of LLM to use ("ollama" or "gemini")
        generate_embedding: Whether to generate an embedding for the result
        db: Database session
    """
    storage_service = StorageService(db)
    jina_service = JinaService()
    llm_service = LLMService(llm_type=llm_type)

    # Get URL
    url = storage_service.get_url_by_id(url_id)
    if not url:
        return

    try:
        # Fetch content with tag filtering and process links if specified
        content_data = await jina_service.fetch_content(
            url=url.url,
            include_tags=url.include_tags,
            exclude_tags=url.exclude_tags,
            process_links=url.process_links
        )

        if content_data.get("error"):
            return

        # Analyze content
        markdown_content = content_data.get("markdown_content", "")
        analysis_result = await llm_service.analyze_content(markdown_content)

        if analysis_result.get("error"):
            return

        # Create result
        result_data = {
            "title": content_data.get("title"),
            "source_url": content_data.get("source_url"),
            "markdown_content": markdown_content,
            "llm_analysis": analysis_result.get("analysis"),
            "llm_type": analysis_result.get("llm_type"),
            "llm_model": analysis_result.get("model")
        }

        result = storage_service.create_result(url_id, result_data)

        # Update URL processed time
        storage_service.update_url_processed_time(url_id)

        # Generate embedding if requested
        if generate_embedding and result:
            embedding_service = EmbeddingService()
            success = embedding_service.add_to_index(
                result_id=result.id,
                text=result.llm_analysis,
                title=result.title or "",
                source=result.source_url or ""
            )

            if success:
                storage_service.update_embedding_status(result.id, True)
    except Exception:
        # Errors are logged in the services
        pass


# Routes
@router.post("/url", response_model=ProcessURLResponse)
async def process_url(request: ProcessURLRequest, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """Process a URL."""
    storage_service = StorageService(db)

    # Check if URL exists
    url = storage_service.get_url_by_id(request.url_id)
    if not url:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"URL with ID {request.url_id} not found"
        )

    # Add background task
    background_tasks.add_task(
        process_url_background,
        url_id=request.url_id,
        llm_type=request.llm_type,
        generate_embedding=request.generate_embedding,
        db=db
    )

    return ProcessURLResponse(
        url_id=request.url_id,
        status="processing",
        message="URL processing started in the background"
    )


@router.post("/batch", response_model=ProcessBatchResponse)
async def process_batch(request: ProcessBatchRequest, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """Process a batch of URLs."""
    storage_service = StorageService(db)
    llm_service = LLMService(llm_type=request.llm_type)

    results = []
    processed = 0
    failed = 0

    # Track valid URL IDs for aggregation
    valid_url_ids = []

    for url_id in request.url_ids:
        # Check if URL exists and is active
        url = storage_service.get_url_by_id(url_id)
        if not url:
            failed += 1
            results.append(ProcessURLResponse(
                url_id=url_id,
                status="error",
                message=f"URL with ID {url_id} not found"
            ))
            continue

        # Skip inactive URLs
        if not url.is_active:
            failed += 1
            results.append(ProcessURLResponse(
                url_id=url_id,
                status="skipped",
                message=f"URL with ID {url_id} is inactive"
            ))
            continue

        # Add to valid URL IDs
        valid_url_ids.append(url_id)

        # Add background task for processing
        background_tasks.add_task(
            process_url_background,
            url_id=url_id,
            llm_type=request.llm_type,
            generate_embedding=request.generate_embedding,
            db=db
        )

        processed += 1
        results.append(ProcessURLResponse(
            url_id=url_id,
            status="processing",
            message="URL processing started in the background"
        ))

    # Add a final task to aggregate the results after all URLs are processed
    if processed > 0:
        async def aggregate_after_processing():
            # Wait a bit to ensure all URLs have been processed
            import asyncio
            await asyncio.sleep(10)  # Wait 10 seconds for processing to complete

            # Get the most recent results for each URL
            analyses = []
            for url_id in valid_url_ids:
                # Get the most recent result for this URL
                result = storage_service.get_latest_result_by_url_id(url_id)
                if result and result.llm_analysis:
                    analyses.append({
                        "id": result.id,
                        "title": result.title or f"Result {result.id}",
                        "llm_analysis": result.llm_analysis
                    })

            if len(analyses) > 1:  # Only aggregate if we have multiple results
                # Aggregate the analyses
                aggregation_result = await llm_service.aggregate_analyses(analyses)

                if not aggregation_result.get("error"):
                    # Save the aggregated result
                    aggregated_data = {
                        "title": f"Batch Analysis ({len(analyses)} sources)",
                        "source_count": len(analyses),
                        "aggregated_analysis": aggregation_result.get("analysis", ""),
                        "llm_type": aggregation_result.get("llm_type"),
                        "llm_model": aggregation_result.get("model"),
                        "result_ids": [r["id"] for r in analyses]
                    }

                    storage_service.create_aggregated_result(aggregated_data)

        background_tasks.add_task(aggregate_after_processing)

    return ProcessBatchResponse(
        total=len(request.url_ids),
        processed=processed,
        failed=failed,
        results=results
    )


@router.post("/aggregate", response_model=AggregateResponse)
async def aggregate_results(request: AggregateRequest, db: Session = Depends(get_db)):
    """Aggregate multiple results into a comprehensive analysis."""
    storage_service = StorageService(db)
    llm_service = LLMService(llm_type=request.llm_type)

    # Get all results
    analyses = []
    for result_id in request.result_ids:
        result = storage_service.get_result_by_id(result_id)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Result with ID {result_id} not found"
            )

        analyses.append({
            "id": result.id,
            "title": result.title or f"Result {result.id}",
            "llm_analysis": result.llm_analysis or ""
        })

    # Aggregate analyses
    aggregation_result = await llm_service.aggregate_analyses(analyses)

    if aggregation_result.get("error"):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error aggregating results: {aggregation_result.get('error')}"
        )

    # Create title if not provided
    title = request.title or f"Aggregated Analysis ({len(analyses)} sources)"

    # Save aggregated result
    aggregated_data = {
        "title": title,
        "source_count": len(analyses),
        "aggregated_analysis": aggregation_result.get("analysis", ""),
        "llm_type": aggregation_result.get("llm_type"),
        "llm_model": aggregation_result.get("model"),
        "result_ids": request.result_ids
    }

    aggregated_result = storage_service.create_aggregated_result(aggregated_data)

    # Convert result_ids from JSON string back to list
    result_ids = json.loads(aggregated_result.result_ids) if isinstance(aggregated_result.result_ids, str) else aggregated_result.result_ids

    return AggregateResponse(
        id=aggregated_result.id,
        title=aggregated_result.title,
        source_count=aggregated_result.source_count,
        aggregated_analysis=aggregated_result.aggregated_analysis,
        llm_type=aggregated_result.llm_type,
        llm_model=aggregated_result.llm_model,
        result_ids=result_ids
    )
